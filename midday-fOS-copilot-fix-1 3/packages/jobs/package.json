{"name": "@faithos/jobs", "version": "0.0.1", "private": true, "scripts": {"dev": "trigger dev", "deploy": "trigger deploy", "typecheck": "tsc --noEmit"}, "exports": {"./schema": "./src/schema.ts"}, "dependencies": {"@faithos/email": "workspace:*", "@faithos/engine": "workspace:*", "@faithos/engine-client": "workspace:*", "@faithos/supabase": "workspace:*", "@fast-csv/format": "5.0.2", "@sindresorhus/slugify": "^2.2.1", "@trigger.dev/sdk": "^3.3.17", "@zip.js/zip.js": "^2.7.62", "camelcase-keys": "^9.1.3", "heic-convert": "1.2.4", "node-xlsx": "^0.24.0", "sharp": "0.34.1"}, "devDependencies": {"@trigger.dev/build": "^3.3.17", "@types/heic-convert": "2.1.0", "trigger.dev": "3.3.17"}}