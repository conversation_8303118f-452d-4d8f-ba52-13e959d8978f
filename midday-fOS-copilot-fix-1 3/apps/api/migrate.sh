#!/bin/bash

# Database Migration Script
# This script applies the database schema to the Supabase database

# Set environment variables
export DATABASE_SESSION_POOLER="**************************************************************************************************/postgres"

echo "Applying database migrations..."

# Check if we can connect to the database
echo "Testing database connection..."
psql "$DATABASE_SESSION_POOLER" -c "SELECT 1;" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "Database connection successful."
    echo "Applying migration file..."
    psql "$DATABASE_SESSION_POOLER" -f migrations/0000_bumpy_chat.sql
    echo "Migration completed."
else
    echo "Unable to connect to database. This may be due to firewall restrictions."
    echo "Please run the following command manually in an environment with database access:"
    echo "psql '$DATABASE_SESSION_POOLER' -f migrations/0000_bumpy_chat.sql"
fi