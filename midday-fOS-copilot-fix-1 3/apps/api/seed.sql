-- Faithos Database Seed Data
-- This script provides initial seed data for the Faithos church management system

-- Insert default transaction categories
INSERT INTO transaction_categories (name, slug, system, vat) VALUES
('Travel', 'travel', true, null),
('Office Supplies', 'office_supplies', true, null),
('Meals', 'meals', true, null),
('Software', 'software', true, null),
('Rent', 'rent', true, null),
('Income', 'income', true, null),
('Equipment', 'equipment', true, null),
('Transfer', 'transfer', true, null),
('Internet and Telephone', 'internet_and_telephone', true, null),
('Facilities Expenses', 'facilities_expenses', true, null),
('Activity', 'activity', true, null),
('Uncategorized', 'uncategorized', true, null),
('Taxes', 'taxes', true, null),
('Other', 'other', true, null),
('Salary', 'salary', true, null),
('Fees', 'fees', true, null)
ON CONFLICT (slug) DO NOTHING;

-- Insert default church service roles
INSERT INTO service_roles (name, description) VALUES
('Worship Leader', 'Leads worship during services'),
('Pastor', 'Delivers sermon and pastoral care'),
('Prayer Leader', 'Leads opening/closing prayers'),
('Scripture Reader', 'Reads Bible passages'),
('Usher', 'Welcomes guests and assists with seating'),
('Sound Technician', 'Manages audio equipment'),
('Video Technician', 'Manages video recording/streaming'),
('Communion Server', 'Assists with communion service'),
('Children''s Ministry Leader', 'Leads children''s programs'),
('Youth Leader', 'Leads youth programs'),
('Greeter', 'Welcomes people at entrance'),
('Musician', 'Plays instruments during service'),
('Choir Member', 'Sings in choir'),
('Altar Server', 'Assists with altar preparations'),
('Announcements', 'Makes announcements during service')
ON CONFLICT (name) DO NOTHING;

-- Insert default event types
INSERT INTO event_types (name, description, color) VALUES
('Sunday Service', 'Regular Sunday worship service', '#3B82F6'),
('Bible Study', 'Bible study sessions', '#10B981'),
('Prayer Meeting', 'Prayer gathering', '#8B5CF6'),
('Community Outreach', 'Community service events', '#F59E0B'),
('Youth Group', 'Youth activities and meetings', '#EF4444'),
('Fellowship', 'Fellowship and social events', '#06B6D4'),
('Special Service', 'Special worship services', '#84CC16'),
('Conference', 'Church conferences and seminars', '#F97316'),
('Baptism', 'Baptism ceremonies', '#3B82F6'),
('Wedding', 'Wedding ceremonies', '#EC4899'),
('Funeral', 'Funeral services', '#6B7280'),
('Holiday Service', 'Holiday and seasonal services', '#DC2626')
ON CONFLICT (name) DO NOTHING;

-- Insert default group types
INSERT INTO group_types (name, description) VALUES
('Life Group', 'Small groups for fellowship and Bible study'),
('Ministry Team', 'Teams serving in specific church ministries'),
('Leadership Circle', 'Church leadership and decision-making groups'),
('Age-based Group', 'Groups organized by age demographics'),
('Interest Group', 'Groups based on shared interests or hobbies'),
('Support Group', 'Groups providing emotional and spiritual support'),
('Prayer Group', 'Groups focused on prayer and intercession'),
('Study Group', 'Groups for specific Bible or topic studies'),
('Service Team', 'Teams focused on community service'),
('Creative Team', 'Teams involved in creative arts and media')
ON CONFLICT (name) DO NOTHING;

-- Insert default song tags
INSERT INTO song_tags (name, description) VALUES
('Worship', 'Songs for worship and adoration'),
('Praise', 'Upbeat praise songs'),
('Prayer', 'Songs suitable for prayer time'),
('Communion', 'Songs for communion service'),
('Christmas', 'Christmas and Advent songs'),
('Easter', 'Easter and Resurrection songs'),
('Baptism', 'Songs for baptism services'),
('Wedding', 'Songs suitable for weddings'),
('Children', 'Songs appropriate for children'),
('Youth', 'Songs popular with youth'),
('Traditional', 'Traditional hymns and songs'),
('Contemporary', 'Modern contemporary worship songs'),
('Gospel', 'Gospel and spiritual songs'),
('Instrumental', 'Instrumental music'),
('Call to Worship', 'Songs for opening worship')
ON CONFLICT (name) DO NOTHING;

-- Insert default check-in stations
INSERT INTO check_in_stations (name, description, location, is_active) VALUES
('Main Entrance', 'Primary check-in location at main entrance', 'Main Lobby', true),
('Children''s Wing', 'Check-in for children''s ministry', 'Children''s Wing Entrance', true),
('Youth Area', 'Check-in for youth programs', 'Youth Center', true),
('VIP/Guest Services', 'Special check-in for VIPs and first-time guests', 'Guest Services Desk', true),
('Side Entrance', 'Secondary check-in location', 'Side Door', false)
ON CONFLICT (name) DO NOTHING;