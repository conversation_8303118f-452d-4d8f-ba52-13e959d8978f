// Test script to verify API exports work correctly
import { appRouter } from './src/trpc/routers/_app';

console.log('🧪 Testing API Router Exports...');

// Check that church management routers are available
const churchRouters = [
  'events',
  'people', 
  'groups',
  'giving',
  'servicePlanning',
  'checkIns'
];

let allRoutersFound = true;

churchRouters.forEach(routerName => {
  if (appRouter[routerName as keyof typeof appRouter]) {
    console.log(`✅ ${routerName} router available`);
  } else {
    console.log(`❌ ${routerName} router missing`);
    allRoutersFound = false;
  }
});

if (allRoutersFound) {
  console.log('\n✅ All church management routers are properly exported');
  console.log('🚀 API is ready for deployment');
} else {
  console.log('\n❌ Some routers are missing');
  process.exit(1);
}

export { appRouter };