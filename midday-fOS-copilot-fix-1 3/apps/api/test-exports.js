"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.appRouter = void 0;
// Test script to verify API exports work correctly
var _app_1 = require("./src/trpc/routers/_app");
Object.defineProperty(exports, "appRouter", { enumerable: true, get: function () { return _app_1.appRouter; } });
console.log('🧪 Testing API Router Exports...');
// Check that church management routers are available
var churchRouters = [
    'events',
    'people',
    'groups',
    'giving',
    'servicePlanning',
    'checkIns'
];
var allRoutersFound = true;
churchRouters.forEach(function (routerName) {
    if (_app_1.appRouter[routerName]) {
        console.log("\u2705 ".concat(routerName, " router available"));
    }
    else {
        console.log("\u274C ".concat(routerName, " router missing"));
        allRoutersFound = false;
    }
});
if (allRoutersFound) {
    console.log('\n✅ All church management routers are properly exported');
    console.log('🚀 API is ready for deployment');
}
else {
    console.log('\n❌ Some routers are missing');
    process.exit(1);
}
