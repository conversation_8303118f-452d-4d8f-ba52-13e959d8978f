import { z } from "zod";

export const getPeopleSchema = z.object({
  cursor: z.string().nullish(),
  pageSize: z.number().min(1).max(100).default(25),
  q: z.string().nullish(),
  sort: z.array(z.string()).default(["firstName", "asc"]).nullish(),
});

export const getPersonByIdSchema = z.object({
  id: z.string().uuid(),
});

export const getPeopleStatsSchema = z.object({
  from: z.string().optional(),
  to: z.string().optional(),
});

export const upsertPersonSchema = z.object({
  id: z.string().uuid().optional(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  email: z.string().email(),
  phone: z.string().optional(),
  membershipStatus: z.enum(["visitor", "regular_attendee", "member", "inactive"]).default("visitor"),
  status: z.enum(["active", "inactive", "transferred", "deceased"]).default("active"),
  avatarUrl: z.string().url().optional(),
  notes: z.string().optional(),
  birthDate: z.string().optional(),
  joinDate: z.string().optional(),
  // Enhanced fields for service integration
  primaryRole: z.string().optional(),
  skills: z.array(z.string()).optional(),
  availability: z.enum(["high", "medium", "low"]).optional(),
  skillLevel: z.enum(["beginner", "intermediate", "advanced", "expert"]).optional(),
  // Role-based access
  userRole: z.enum(["admin", "volunteer", "leader", "member"]).default("member"),
  canScheduleServices: z.boolean().default(false),
  canManageGroups: z.boolean().default(false),
  canViewReports: z.boolean().default(false),
});

// Role-based permission schemas
export const getPersonPermissionsSchema = z.object({
  personId: z.string().uuid(),
});

export const updatePersonPermissionsSchema = z.object({
  personId: z.string().uuid(),
  userRole: z.enum(["admin", "volunteer", "leader", "member"]),
  canScheduleServices: z.boolean(),
  canManageGroups: z.boolean(),
  canViewReports: z.boolean(),
});

// Service history and relationships
export const getPersonServiceHistorySchema = z.object({
  personId: z.string().uuid(),
  from: z.string().optional(),
  to: z.string().optional(),
  status: z.enum(["confirmed", "pending", "declined", "completed"]).optional(),
});

export const getPersonGroupsSchema = z.object({
  personId: z.string().uuid(),
  includeInactive: z.boolean().default(false),
});

export const deletePersonSchema = z.object({
  id: z.string().uuid(),
});