import { z } from "zod";

export const getDonationsSchema = z.object({
  cursor: z.string().optional(),
  limit: z.number().min(1).max(100).optional(),
  q: z.string().optional(),
  sort: z.array(z.string()).length(2).optional(),
  from: z.string().optional(),
  to: z.string().optional(),
});

export const getDonationByIdSchema = z.object({
  id: z.string().uuid(),
});

export const getGivingStatsSchema = z.object({
  from: z.string().optional(),
  to: z.string().optional(),
});

export const upsertDonationSchema = z.object({
  id: z.string().uuid().optional(),
  amount: z.number().positive(),
  donationDate: z.string(),
  method: z.enum(["cash", "check", "card", "online", "bank_transfer"]),
  donorId: z.string().uuid().optional(),
  donorName: z.string().optional(),
  anonymous: z.boolean().optional(),
  note: z.string().optional(),
  reference: z.string().optional(),
  checkNumber: z.string().optional(),
  recurring: z.boolean().optional(),
  campaignId: z.string().uuid().optional(),
});

export const deleteDonationSchema = z.object({
  id: z.string().uuid(),
});