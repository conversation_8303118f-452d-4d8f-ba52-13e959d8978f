import { z } from "zod";

export const getGroupsSchema = z.object({
  cursor: z.string().nullish(),
  pageSize: z.number().min(1).max(100).default(25),
  q: z.string().nullish(),
  sort: z.array(z.string()).default(["name", "asc"]).nullish(),
  category: z.string().nullish(),
  groupType: z.enum(["open", "closed", "private"]).nullish(),
  isActive: z.boolean().nullish(),
});

export const getGroupByIdSchema = z.object({
  id: z.string().uuid(),
});

export const getGroupsStatsSchema = z.object({
  from: z.string().optional(),
  to: z.string().optional(),
});

export const upsertGroupSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  description: z.string().optional(),
  category: z.string().optional(), // "small_group", "ministry", "committee", "class", "team"
  groupType: z.enum(["open", "closed", "private"]).default("open"),
  leaderId: z.string().uuid().optional(),
  assistantLeaders: z.array(z.string().uuid()).optional(),
  meetingTime: z.string().optional(),
  meetingLocation: z.string().optional(),
  meetingFrequency: z.string().optional(), // weekly, biweekly, monthly, etc.
  childcareProvided: z.boolean().default(false),
  ageGroupMin: z.number().positive().optional(),
  ageGroupMax: z.number().positive().optional(),
  gender: z.enum(["mixed", "male", "female"]).optional(),
  isActive: z.boolean().default(true),
  maxMembers: z.number().positive().optional(),
  registrationOpen: z.boolean().default(true),
  registrationStartDate: z.string().optional(),
  registrationEndDate: z.string().optional(),
  groupStartDate: z.string().optional(),
  groupEndDate: z.string().optional(),
  cost: z.number().optional(),
  tags: z.array(z.string()).optional(),
  notes: z.string().optional(),
});

export const deleteGroupSchema = z.object({
  id: z.string().uuid(),
});

// Group Memberships
export const getGroupMembershipsSchema = z.object({
  groupId: z.string().uuid(),
  isActive: z.boolean().optional(),
});

export const upsertGroupMembershipSchema = z.object({
  id: z.string().uuid().optional(),
  groupId: z.string().uuid(),
  personId: z.string().uuid(),
  role: z.string().default("member"), // member, leader, co-leader
  isActive: z.boolean().default(true),
});

export const deleteGroupMembershipSchema = z.object({
  id: z.string().uuid(),
});

// Bulk operations
export const bulkAddMembersSchema = z.object({
  groupId: z.string().uuid(),
  personIds: z.array(z.string().uuid()),
  role: z.string().default("member"),
});

export const bulkRemoveMembersSchema = z.object({
  groupId: z.string().uuid(),
  membershipIds: z.array(z.string().uuid()),
});