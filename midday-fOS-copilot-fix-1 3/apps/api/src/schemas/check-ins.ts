import { z } from "zod";

// Check-in Events
export const getCheckInEventsSchema = z.object({
  limit: z.number().min(1).max(100).default(20),
  page: z.number().min(1).default(1),
  search: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
});

export const createCheckInEventSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  eventDate: z.string(),
  endDate: z.string().optional(),
  location: z.string().optional(),
  requiresCheckIn: z.boolean().default(true),
  allowSelfCheckIn: z.boolean().default(false),
  printBadges: z.boolean().default(true),
  maxCapacity: z.number().optional(),
  ageGroupMin: z.number().optional(),
  ageGroupMax: z.number().optional(),
  securityCode: z.string().optional(),
});

export const updateCheckInEventSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  description: z.string().optional(),
  eventDate: z.string().optional(),
  endDate: z.string().optional(),
  location: z.string().optional(),
  requiresCheckIn: z.boolean().optional(),
  allowSelfCheckIn: z.boolean().optional(),
  printBadges: z.boolean().optional(),
  maxCapacity: z.number().optional(),
  ageGroupMin: z.number().optional(),
  ageGroupMax: z.number().optional(),
  securityCode: z.string().optional(),
});

export const deleteCheckInEventSchema = z.object({
  id: z.string(),
});

// Check-ins
export const getCheckInsSchema = z.object({
  limit: z.number().min(1).max(100).default(20),
  page: z.number().min(1).default(1),
  eventId: z.string().optional(),
  personId: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
});

export const createCheckInSchema = z.object({
  eventId: z.string(),
  personId: z.string(),
  checkedInAt: z.string().optional(),
  checkedInBy: z.string(),
  notes: z.string().optional(),
  securityCode: z.string().optional(),
  guardian: z.string().optional(),
  guardianPhone: z.string().optional(),
});

export const checkOutSchema = z.object({
  id: z.string(),
  checkedOutAt: z.string().optional(),
});

// Stats
export const getCheckInStatsSchema = z.object({
  eventId: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
});

// Types
export type GetCheckInEventsInput = z.infer<typeof getCheckInEventsSchema>;
export type CreateCheckInEventInput = z.infer<typeof createCheckInEventSchema>;
export type UpdateCheckInEventInput = z.infer<typeof updateCheckInEventSchema>;
export type DeleteCheckInEventInput = z.infer<typeof deleteCheckInEventSchema>;
export type GetCheckInsInput = z.infer<typeof getCheckInsSchema>;
export type CreateCheckInInput = z.infer<typeof createCheckInSchema>;
export type CheckOutInput = z.infer<typeof checkOutSchema>;
export type GetCheckInStatsInput = z.infer<typeof getCheckInStatsSchema>;