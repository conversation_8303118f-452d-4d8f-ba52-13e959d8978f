import { z } from "zod";

export const getServicePlansSchema = z.object({
  cursor: z.string().optional(),
  limit: z.number().min(1).max(100).optional(),
  q: z.string().optional(),
  sort: z.array(z.string()).length(2).optional(),
  from: z.string().optional(),
  to: z.string().optional(),
  status: z.string().optional(),
});

export const getServicePlanByIdSchema = z.object({
  id: z.string().uuid(),
});

export const getServicePlanningStatsSchema = z.object({
  from: z.string().optional(),
  to: z.string().optional(),
});

export const upsertServicePlanSchema = z.object({
  id: z.string().uuid().optional(),
  title: z.string().min(1),
  serviceDate: z.string(),
  serviceTime: z.string().optional(),
  serviceType: z.enum([
    "regular", 
    "special", 
    "holiday", 
    "wedding", 
    "funeral", 
    "baptism", 
    "communion", 
    "prayer", 
    "youth", 
    "children", 
    "small_group", 
    "conference", 
    "retreat", 
    "outreach", 
    "missions", 
    "other"
  ]),
  theme: z.string().optional(),
  scripture: z.string().optional(),
  status: z.enum(["draft", "planning", "in_review", "approved", "in_progress", "live", "completed", "cancelled"]),
  location: z.string().optional(),
  estimatedAttendance: z.number().positive().optional(),
  notes: z.string().optional(),
  // Enhanced fields
  description: z.string().optional(),
  templateId: z.string().uuid().optional(),
  venue: z.string().optional(),
  campus: z.string().optional(),
  estimatedDuration: z.number().positive().optional(),
  sermonSeries: z.string().optional(),
  sermonTitle: z.string().optional(),
  rehearsalDate: z.string().optional(),
  rehearsalLocation: z.string().optional(),
  setupTime: z.number().optional(),
  soundCheckTime: z.number().optional(),
});

export const deleteServicePlanSchema = z.object({
  id: z.string().uuid(),
});

// Service Plan Volunteers
export const getServicePlanVolunteersSchema = z.object({
  servicePlanId: z.string().uuid(),
  status: z.enum(["invited", "confirmed", "declined", "cancelled"]).optional(),
});

export const upsertServicePlanVolunteerSchema = z.object({
  id: z.string().uuid().optional(),
  servicePlanId: z.string().uuid(),
  userId: z.string().uuid(),
  positionId: z.string().uuid(),
  status: z.enum(["invited", "confirmed", "declined", "cancelled"]),
  notes: z.string().optional(),
  substituteUserId: z.string().uuid().optional(),
});

export const deleteServicePlanVolunteerSchema = z.object({
  id: z.string().uuid(),
});

// Service Plan Songs
export const getServicePlanSongsSchema = z.object({
  servicePlanId: z.string().uuid(),
});

export const upsertServicePlanSongSchema = z.object({
  id: z.string().uuid().optional(),
  servicePlanId: z.string().uuid(),
  songId: z.string().uuid().optional(),
  serviceElementId: z.string().uuid().optional(),
  orderIndex: z.number().nonnegative(),
  selectedKey: z.string().optional(),
  tempo: z.number().positive().optional(),
  duration: z.number().positive().optional(),
  arrangementId: z.string().uuid().optional(),
  isReady: z.boolean().optional(),
  notes: z.string().optional(),
});

export const deleteServicePlanSongSchema = z.object({
  id: z.string().uuid(),
});

// Service Plan Comments
export const getServicePlanCommentsSchema = z.object({
  servicePlanId: z.string().uuid(),
  serviceElementId: z.string().uuid().optional(),
  isResolved: z.boolean().optional(),
});

export const upsertServicePlanCommentSchema = z.object({
  id: z.string().uuid().optional(),
  servicePlanId: z.string().uuid(),
  serviceElementId: z.string().uuid().optional(),
  content: z.string().min(1),
  priority: z.enum(["low", "medium", "high", "critical"]).optional(),
  isResolved: z.boolean().optional(),
  resolvedById: z.string().uuid().optional(),
  resolvedAt: z.string().optional(),
});

export const deleteServicePlanCommentSchema = z.object({
  id: z.string().uuid(),
});

// Song Library
export const getSongsSchema = z.object({
  cursor: z.string().optional(),
  limit: z.number().min(1).max(100).optional(),
  q: z.string().optional(),
  sort: z.array(z.string()).length(2).optional(),
  genre: z.string().optional(),
  status: z.enum(["draft", "approved", "archived"]).optional(),
});

export const upsertSongSchema = z.object({
  id: z.string().uuid().optional(),
  title: z.string().min(1),
  artist: z.string().optional(),
  album: z.string().optional(),
  year: z.number().optional(),
  originalKey: z.string().optional(),
  tempo: z.number().positive().optional(),
  timeSignature: z.string().optional(),
  genre: z.string().optional(),
  themes: z.array(z.string()).optional(),
  copyrightInfo: z.string().optional(),
  ccliNumber: z.string().optional(),
  lyrics: z.string().optional(),
  chords: z.string().optional(),
  notes: z.string().optional(),
  status: z.enum(["draft", "approved", "archived"]),
  audioUrl: z.string().url().optional(),
  videoUrl: z.string().url().optional(),
  spotifyUrl: z.string().url().optional(),
  youtubeUrl: z.string().url().optional(),
});

export const deleteSongSchema = z.object({
  id: z.string().uuid(),
});

export const getSongArrangementsSchema = z.object({
  songId: z.string().uuid(),
});

// Team Positions
export const getTeamPositionsSchema = z.object({
  q: z.string().optional(),
  department: z.string().optional(),
});

export const upsertTeamPositionSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  description: z.string().optional(),
  department: z.string().optional(),
  requiredSkills: z.array(z.string()).optional(),
  maxVolunteers: z.number().positive().optional(),
  isActive: z.boolean().optional(),
});

export const deleteTeamPositionSchema = z.object({
  id: z.string().uuid(),
});

// Templates
export const getServicePlanTemplatesSchema = z.object({
  q: z.string().optional(),
  serviceType: z.string().optional(),
});

export const upsertServicePlanTemplateSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  description: z.string().optional(),
  serviceType: z.enum([
    "regular", 
    "special", 
    "holiday", 
    "wedding", 
    "funeral", 
    "baptism", 
    "communion", 
    "prayer", 
    "youth", 
    "children", 
    "small_group", 
    "conference", 
    "retreat", 
    "outreach", 
    "missions", 
    "other"
  ]),
  estimatedDuration: z.number().positive().optional(),
  isPublic: z.boolean().optional(),
  elements: z.array(z.object({
    type: z.string(),
    title: z.string(),
    description: z.string().optional(),
    estimatedDuration: z.number().positive().optional(),
    orderIndex: z.number().nonnegative(),
  })).optional(),
});

export const deleteServicePlanTemplateSchema = z.object({
  id: z.string().uuid(),
});