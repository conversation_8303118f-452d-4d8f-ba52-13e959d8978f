import { z } from "zod";

// Event management schemas
export const createEventSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  imageUrl: z.string().optional(),
  
  // Event scheduling
  startDate: z.string(),
  endDate: z.string().optional(),
  timeZone: z.string().default("UTC"),
  isAllDay: z.boolean().default(false),
  
  // Event types and categories
  eventType: z.enum(["conference", "workshop", "service", "retreat", "social", "fundraiser", "meeting", "training"]),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  
  // Location information
  location: z.string().optional(),
  address: z.string().optional(),
  coordinates: z.object({
    lat: z.number(),
    lng: z.number(),
  }).optional(),
  virtualLink: z.string().optional(),
  isVirtual: z.boolean().default(false),
  isHybrid: z.boolean().default(false),
  
  // Registration settings
  registrationOpen: z.boolean().default(true),
  registrationStartDate: z.string().optional(),
  registrationEndDate: z.string().optional(),
  earlyBirdEndDate: z.string().optional(),
  
  // Capacity and waitlist
  maxCapacity: z.number().positive().optional(),
  waitlistEnabled: z.boolean().default(true),
  waitlistLimit: z.number().positive().optional(),
  
  // Pricing
  isFree: z.boolean().default(true),
  basePrice: z.number().min(0).optional(),
  earlyBirdPrice: z.number().min(0).optional(),
  currency: z.string().default("USD"),
  
  // Age and demographic restrictions
  minAge: z.number().min(0).optional(),
  maxAge: z.number().min(0).optional(),
  genderRestriction: z.enum(["none", "male", "female"]).default("none"),
  membershipRequired: z.boolean().default(false),
  
  // Event behavior
  requiresApproval: z.boolean().default(false),
  allowGroupRegistration: z.boolean().default(true),
  maxGroupSize: z.number().positive().optional(),
  
  // Check-in integration
  requiresCheckIn: z.boolean().default(true),
  allowSelfCheckIn: z.boolean().default(false),
  printBadges: z.boolean().default(true),
  securityRequired: z.boolean().default(false),
  
  // Communication settings
  confirmationEmailTemplate: z.string().optional(),
  reminderEmailTemplate: z.string().optional(),
  followUpEmailTemplate: z.string().optional(),
  
  // Status and visibility
  status: z.enum(["draft", "published", "cancelled", "completed"]).default("draft"),
  isPublic: z.boolean().default(true),
  isRecurring: z.boolean().default(false),
  recurrenceRule: z.string().optional(),
  
  // Administrative
  organizers: z.array(z.string()).optional(),
  
  // Metadata and custom fields
  customFields: z.record(z.any()).optional(),
  metadata: z.record(z.any()).optional(),
});

export const updateEventSchema = z.object({
  id: z.string(),
}).merge(createEventSchema.partial());

export const deleteEventSchema = z.object({
  id: z.string(),
});

export const getEventsSchema = z.object({
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
  status: z.enum(["draft", "published", "cancelled", "completed"]).optional(),
  eventType: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

export const getEventSchema = z.object({
  id: z.string(),
});

// Event registration form schemas
export const createRegistrationFormSchema = z.object({
  eventId: z.string(),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  submitButtonText: z.string().default("Register"),
  
  // Form fields as JSON schema
  fields: z.array(z.object({
    id: z.string(),
    type: z.enum(["text", "email", "phone", "number", "select", "checkbox", "radio", "textarea", "date", "file"]),
    label: z.string(),
    placeholder: z.string().optional(),
    required: z.boolean().default(false),
    options: z.array(z.string()).optional(), // For select, checkbox, radio
    validation: z.object({
      min: z.number().optional(),
      max: z.number().optional(),
      pattern: z.string().optional(),
    }).optional(),
  })),
  
  requiresPayment: z.boolean().default(false),
  allowMultipleSubmissions: z.boolean().default(false),
  theme: z.record(z.any()).optional(),
});

export const updateRegistrationFormSchema = z.object({
  id: z.string(),
}).merge(createRegistrationFormSchema.partial().omit({ eventId: true }));

// Event registration schemas
export const createRegistrationSchema = z.object({
  eventId: z.string(),
  personId: z.string().optional(), // Null if registering as guest
  
  // Registrant information (for guests or overrides)
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  
  // Registration details
  registrationType: z.enum(["individual", "group", "leader"]).default("individual"),
  groupId: z.string().optional(),
  
  // Form responses
  formResponses: z.record(z.any()).optional(),
  
  // Special requirements
  dietaryRestrictions: z.string().optional(),
  accessibilityNeeds: z.string().optional(),
  emergencyContact: z.object({
    name: z.string(),
    phone: z.string(),
    relationship: z.string(),
  }).optional(),
  medicalInfo: z.string().optional(),
  
  // Administrative
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  source: z.string().default("web"),
  referralSource: z.string().optional(),
  
  // Communication preferences
  emailOptIn: z.boolean().default(true),
  smsOptIn: z.boolean().default(false),
  
  // Metadata
  metadata: z.record(z.any()).optional(),
});

export const updateRegistrationSchema = z.object({
  id: z.string(),
  status: z.enum(["pending", "confirmed", "cancelled", "waitlisted", "no_show"]).optional(),
  paymentStatus: z.enum(["pending", "paid", "refunded", "failed"]).optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  
  // Check-in related
  checkedIn: z.boolean().optional(),
  checkInTime: z.string().optional(),
});

export const getRegistrationsSchema = z.object({
  eventId: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
  status: z.enum(["pending", "confirmed", "cancelled", "waitlisted", "no_show"]).optional(),
  paymentStatus: z.enum(["pending", "paid", "refunded", "failed"]).optional(),
  search: z.string().optional(),
});

// Event session schemas
export const createSessionSchema = z.object({
  eventId: z.string(),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  sessionNumber: z.number().positive().optional(),
  
  // Timing
  startTime: z.string(),
  endTime: z.string().optional(),
  
  // Location (can override event location)
  location: z.string().optional(),
  virtualLink: z.string().optional(),
  
  // Capacity (can be different from main event)
  maxCapacity: z.number().positive().optional(),
  
  // Requirements
  requiresSeparateRegistration: z.boolean().default(false),
  additionalCost: z.number().min(0).optional(),
  
  // Speakers/facilitators
  facilitators: z.array(z.object({
    name: z.string(),
    title: z.string().optional(),
    bio: z.string().optional(),
    imageUrl: z.string().optional(),
    contactInfo: z.string().optional(),
  })).optional(),
  
  // Resources
  materials: z.array(z.object({
    title: z.string(),
    url: z.string(),
    type: z.string(),
  })).optional(),
});

export const updateSessionSchema = z.object({
  id: z.string(),
}).merge(createSessionSchema.partial().omit({ eventId: true }));

// Event resource schemas
export const createResourceSchema = z.object({
  eventId: z.string(),
  sessionId: z.string().optional(),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  resourceType: z.enum(["document", "link", "video", "audio", "image"]),
  
  // File information
  fileUrl: z.string().optional(),
  fileName: z.string().optional(),
  fileSize: z.number().optional(),
  mimeType: z.string().optional(),
  
  // External links
  externalUrl: z.string().optional(),
  
  // Access control
  isPublic: z.boolean().default(false),
  availableAfterRegistration: z.boolean().default(true),
  availableAfterEvent: z.boolean().default(true),
  
  // Organization
  category: z.string().optional(),
  sortOrder: z.number().default(0),
});

// Event communication schemas
export const createCommunicationSchema = z.object({
  eventId: z.string(),
  type: z.enum(["confirmation", "reminder", "follow_up", "cancellation", "update"]),
  subject: z.string().min(1, "Subject is required"),
  content: z.string().min(1, "Content is required"),
  
  // Delivery settings
  deliveryMethod: z.enum(["email", "sms", "push", "in_app"]),
  scheduledFor: z.string().optional(),
  isAutomatic: z.boolean().default(false),
  
  // Targeting
  targetAudience: z.enum(["all", "confirmed", "waitlist", "checked_in"]),
  customFilter: z.record(z.any()).optional(),
  
  // Administrative
  template: z.string().optional(),
});

// Event analytics schemas
export const getEventStatsSchema = z.object({
  eventId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

export const getEventAnalyticsSchema = z.object({
  eventId: z.string(),
});

// Event template schemas
export const createEventTemplateSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  eventType: z.string(),
  templateData: z.record(z.any()), // Saved event configuration
});

export const getEventTemplatesSchema = z.object({
  eventType: z.string().optional(),
  limit: z.number().min(1).max(50).default(20),
});

// Check-in schemas (enhanced)
export const bulkCheckInSchema = z.object({
  eventId: z.string(),
  registrations: z.array(z.object({
    registrationId: z.string(),
    securityCode: z.string().optional(),
    notes: z.string().optional(),
  })),
});

export const generateBadgesSchema = z.object({
  eventId: z.string(),
  registrationIds: z.array(z.string()).optional(), // If empty, generate for all
  template: z.string().optional(),
});

export const eventCheckInStatsSchema = z.object({
  eventId: z.string(),
});