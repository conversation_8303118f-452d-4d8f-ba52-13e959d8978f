import type { Database } from "@api/db";
import { groups, events, groupMemberships, people } from "@api/db/schema";
import { and, desc, eq } from "drizzle-orm";

export type GetGroupsParams = {
  teamId: string;
  cursor?: string | null;
  pageSize?: number;
  q?: string | null;
  sort?: string[] | null;
};

export const getGroups = async (
  db: Database,
  params: GetGroupsParams,
) => {
  const { teamId, pageSize = 25 } = params;

  const result = await db
    .select()
    .from(groups)
    .where(eq(groups.teamId, teamId))
    .orderBy(desc(groups.createdAt))
    .limit(pageSize);

  return { data: result };
};

export const getGroupById = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  const [group] = await db
    .select()
    .from(groups)
    .where(and(eq(groups.id, id), eq(groups.teamId, teamId)));

  return group;
};

export const createGroup = async (
  db: Database,
  teamId: string,
  userId: string,
  data: any,
) => {
  const [group] = await db
    .insert(groups)
    .values({
      ...data,
      teamId,
      createdBy: userId,
    })
    .returning();

  return group;
};

export const updateGroup = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  const [group] = await db
    .update(groups)
    .set(data)
    .where(and(eq(groups.id, data.id), eq(groups.teamId, teamId)))
    .returning();

  return group;
};

export const deleteGroup = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  await db
    .delete(groups)
    .where(and(eq(groups.id, id), eq(groups.teamId, teamId)));

  return { success: true };
};

export const getGroupEvents = async (
  db: Database,
  teamId: string,
  groupId: string,
) => {
  const result = await db
    .select({
      id: events.id,
      title: events.title,
      description: events.description,
      startDate: events.startDate,
      endDate: events.endDate,
      location: events.location,
      createdAt: events.createdAt,
    })
    .from(events)
    .where(
      and(
        eq(events.teamId, teamId),
        // This would need to be joined with an event_groups table if it exists
        // For now, return all events for the team
      ),
    )
    .orderBy(desc(events.startDate));

  return result;
};

export const getGroupMembers = async (
  db: Database,
  teamId: string,
  groupId: string,
) => {
  const result = await db
    .select({
      id: people.id,
      firstName: people.firstName,
      lastName: people.lastName,
      email: people.email,
      phone: people.phone,
      joinedDate: groupMemberships.joinedDate,
      role: groupMemberships.role,
    })
    .from(groupMemberships)
    .innerJoin(people, eq(groupMemberships.personId, people.id))
    .where(
      and(
        eq(groupMemberships.groupId, groupId),
        eq(people.teamId, teamId)
      )
    );

  return result;
};

export const addGroupMember = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  const [membership] = await db
    .insert(groupMemberships)
    .values({
      groupId: data.groupId,
      personId: data.personId,
      role: data.role || "member",
      notes: data.notes,
    })
    .returning();

  return membership;
};

export const removeGroupMember = async (
  db: Database,
  teamId: string,
  groupId: string,
  personId: string,
) => {
  await db
    .delete(groupMemberships)
    .where(
      and(
        eq(groupMemberships.groupId, groupId),
        eq(groupMemberships.personId, personId)
      )
    );

  return { success: true };
};