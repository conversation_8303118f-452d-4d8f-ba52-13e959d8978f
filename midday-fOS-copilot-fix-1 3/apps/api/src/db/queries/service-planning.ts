import type { Database } from "@api/db";
import { servicePlans, serviceAssignments, serviceRoles } from "@api/db/schema";
import { and, desc, eq } from "drizzle-orm";

export type GetServicePlansParams = {
  teamId: string;
  cursor?: string | null;
  pageSize?: number;
  q?: string | null;
  sort?: string[] | null;
};

export const getServicePlans = async (
  db: Database,
  params: GetServicePlansParams,
) => {
  const { teamId, pageSize = 25 } = params;

  const result = await db
    .select()
    .from(servicePlans)
    .where(eq(servicePlans.teamId, teamId))
    .orderBy(desc(servicePlans.createdAt))
    .limit(pageSize);

  return { data: result };
};

export const getServicePlanById = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  const [plan] = await db
    .select()
    .from(servicePlans)
    .where(and(eq(servicePlans.id, id), eq(servicePlans.teamId, teamId)));

  return plan;
};

export const createServicePlan = async (
  db: Database,
  teamId: string,
  userId: string,
  data: any,
) => {
  const [plan] = await db
    .insert(servicePlans)
    .values({
      ...data,
      teamId,
      createdBy: userId,
    })
    .returning();

  return plan;
};

export const updateServicePlan = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  const [plan] = await db
    .update(servicePlans)
    .set(data)
    .where(and(eq(servicePlans.id, data.id), eq(servicePlans.teamId, teamId)))
    .returning();

  return plan;
};

export const deleteServicePlan = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  await db
    .delete(servicePlans)
    .where(and(eq(servicePlans.id, id), eq(servicePlans.teamId, teamId)));

  return { success: true };
};

export const getServiceRoles = async (
  db: Database,
  teamId: string,
) => {
  const result = await db
    .select()
    .from(serviceRoles);

  return result;
};

export const createServiceRole = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  const [role] = await db
    .insert(serviceRoles)
    .values({
      ...data,
      teamId,
    })
    .returning();

  return role;
};

export const addServiceRole = createServiceRole;
export const removeServiceRole = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  await db
    .delete(serviceRoles)
    .where(eq(serviceRoles.id, data.id));

  return { success: true };
};

export const assignServiceRole = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  const [assignment] = await db
    .insert(serviceAssignments)
    .values({
      servicePlanId: data.servicePlanId,
      personId: data.personId,
      roleId: data.roleId,
      notes: data.notes,
    })
    .returning();

  return assignment;
};

export const getServiceSongs = async (
  db: Database,
  teamId: string,
  servicePlanId: string,
) => {
  // Return empty array for now since we don't have song tables set up
  return [];
};

export const addServiceSong = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  // Return mock response for now
  return { success: true };
};

export const removeServiceSong = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  // Return mock response for now
  return { success: true };
};