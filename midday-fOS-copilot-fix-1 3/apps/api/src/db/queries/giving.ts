import type { Database } from "@api/db";
import { donations } from "@api/db/schema";
import { and, desc, eq, sum, count } from "drizzle-orm";

export type GetDonationsParams = {
  teamId: string;
  cursor?: string | null;
  pageSize?: number;
  q?: string | null;
  sort?: string[] | null;
};

export const getDonations = async (
  db: Database,
  params: GetDonationsParams,
) => {
  const { teamId, pageSize = 25 } = params;

  const result = await db
    .select()
    .from(donations)
    .where(eq(donations.teamId, teamId))
    .orderBy(desc(donations.donationDate))
    .limit(pageSize);

  return { data: result };
};

export const getDonationById = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  const [donation] = await db
    .select()
    .from(donations)
    .where(and(eq(donations.id, id), eq(donations.teamId, teamId)));

  return donation;
};

export const createDonation = async (
  db: Database,
  teamId: string,
  userId: string,
  data: any,
) => {
  const [donation] = await db
    .insert(donations)
    .values({
      ...data,
      teamId,
      recordedBy: userId,
    })
    .returning();

  return donation;
};

export const updateDonation = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  const [donation] = await db
    .update(donations)
    .set(data)
    .where(and(eq(donations.id, data.id), eq(donations.teamId, teamId)))
    .returning();

  return donation;
};

export const deleteDonation = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  await db
    .delete(donations)
    .where(and(eq(donations.id, id), eq(donations.teamId, teamId)));

  return { success: true };
};

export const getDonationStats = async (
  db: Database,
  teamId: string,
) => {
  const [totalStats] = await db
    .select({
      totalAmount: sum(donations.amount),
      totalCount: count(donations.id),
    })
    .from(donations)
    .where(eq(donations.teamId, teamId));

  return totalStats;
};

export const createGivingCampaign = async (
  db: Database,
  teamId: string,
  userId: string,
  data: any,
) => {
  // For now, campaigns are stored as special entries in donations with a campaign reference
  // In a future iteration, you could create a separate campaigns table
  const [campaign] = await db
    .insert(donations)
    .values({
      ...data,
      teamId,
      createdBy: userId,
      donationType: "campaign",
      fundDesignation: data.name,
    })
    .returning();

  return campaign;
};

export const getGivingCampaigns = async (
  db: Database,
  params: { teamId: string },
) => {
  // For now, return empty array as campaigns might not be implemented yet
  return { data: [] };
};

export const getDonationsByPerson = async (
  db: Database,
  teamId: string,
  personId: string,
) => {
  const result = await db
    .select()
    .from(donations)
    .where(
      and(
        eq(donations.teamId, teamId),
        eq(donations.personId, personId)
      )
    )
    .orderBy(desc(donations.donationDate));

  return result;
};