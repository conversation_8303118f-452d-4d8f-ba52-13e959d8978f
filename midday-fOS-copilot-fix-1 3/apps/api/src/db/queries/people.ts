import type { Database } from "@api/db";
import { people, events, groupMemberships, groups } from "@api/db/schema";
import { and, desc, eq, ilike } from "drizzle-orm";

export type GetPeopleParams = {
  teamId: string;
  cursor?: string | null;
  pageSize?: number;
  q?: string | null;
  sort?: string[] | null;
};

export const getPeople = async (
  db: Database,
  params: GetPeopleParams,
) => {
  const { teamId, pageSize = 25, q } = params;

  const whereConditions = [eq(people.teamId, teamId)];

  if (q) {
    whereConditions.push(
      ilike(people.firstName, `%${q}%`)
    );
  }

  const result = await db
    .select({
      id: people.id,
      firstName: people.firstName,
      lastName: people.lastName,
      email: people.email,
      phone: people.phone,
      membershipStatus: people.membershipStatus,
      createdAt: people.createdAt,
    })
    .from(people)
    .where(and(...whereConditions))
    .orderBy(desc(people.createdAt))
    .limit(pageSize);

  return { data: result };
};

export const getPersonById = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  const [person] = await db
    .select()
    .from(people)
    .where(and(eq(people.id, id), eq(people.teamId, teamId)));

  return person;
};

export const createPerson = async (
  db: Database,
  teamId: string,
  userId: string,
  data: any,
) => {
  const [person] = await db
    .insert(people)
    .values({
      ...data,
      teamId,
      createdBy: userId,
    })
    .returning();

  return person;
};

export const updatePerson = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  const [person] = await db
    .update(people)
    .set(data)
    .where(and(eq(people.id, data.id), eq(people.teamId, teamId)))
    .returning();

  return person;
};

export const deletePerson = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  await db
    .delete(people)
    .where(and(eq(people.id, id), eq(people.teamId, teamId)));

  return { success: true };
};

export const getPersonGroups = async (
  db: Database,
  teamId: string,
  personId: string,
) => {
  const result = await db
    .select({
      id: groups.id,
      name: groups.name,
      description: groups.description,
      createdAt: groups.createdAt,
    })
    .from(groupMemberships)
    .innerJoin(groups, eq(groupMemberships.groupId, groups.id))
    .where(
      and(
        eq(groupMemberships.personId, personId),
        eq(groups.teamId, teamId)
      )
    );

  return result;
};

export const getPersonEvents = async (
  db: Database,
  teamId: string,
  personId: string,
) => {
  const result = await db
    .select({
      id: events.id,
      title: events.title,
      description: events.description,
      startDate: events.startDate,
      endDate: events.endDate,
      location: events.location,
      createdAt: events.createdAt,
    })
    .from(events)
    .where(
      and(
        eq(events.teamId, teamId),
      )
    )
    .orderBy(desc(events.startDate));

  return result;
};