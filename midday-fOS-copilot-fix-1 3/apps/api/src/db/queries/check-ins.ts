import type { Database } from "@api/db";
import { checkIns, checkInEvents } from "@api/db/schema";
import { and, desc, eq } from "drizzle-orm";

export type GetCheckInsParams = {
  teamId: string;
  cursor?: string | null;
  pageSize?: number;
  q?: string | null;
  sort?: string[] | null;
};

export const getCheckIns = async (
  db: Database,
  params: GetCheckInsParams,
) => {
  const { teamId, pageSize = 25 } = params;

  // Get check-ins through events that belong to the team
  const result = await db
    .select({
      id: checkIns.id,
      personId: checkIns.personId,
      checkInTime: checkIns.checkInTime,
      checkOutTime: checkIns.checkOutTime,
      status: checkIns.status,
      notes: checkIns.notes,
      eventName: checkInEvents.name,
    })
    .from(checkIns)
    .innerJoin(checkInEvents, eq(checkIns.checkInEventId, checkInEvents.id))
    .where(eq(checkInEvents.teamId, teamId))
    .orderBy(desc(checkIns.checkInTime))
    .limit(pageSize);

  return { data: result };
};

export const getCheckInById = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  const [checkIn] = await db
    .select()
    .from(checkIns)
    .innerJoin(checkInEvents, eq(checkIns.checkInEventId, checkInEvents.id))
    .where(and(eq(checkIns.id, id), eq(checkInEvents.teamId, teamId)));

  return checkIn;
};

export const createCheckIn = async (
  db: Database,
  teamId: string,
  userId: string,
  data: any,
) => {
  const [checkIn] = await db
    .insert(checkIns)
    .values({
      ...data,
      checkedInBy: userId,
    })
    .returning();

  return checkIn;
};

export const updateCheckIn = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  const [checkIn] = await db
    .update(checkIns)
    .set(data)
    .where(eq(checkIns.id, data.id))
    .returning();

  return checkIn;
};

export const deleteCheckIn = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  await db
    .delete(checkIns)
    .where(eq(checkIns.id, id));

  return { success: true };
};

export const getCheckInEvents = async (
  db: Database,
  teamId: string,
) => {
  const result = await db
    .select()
    .from(checkInEvents)
    .where(eq(checkInEvents.teamId, teamId))
    .orderBy(desc(checkInEvents.eventDate));

  return result;
};

export const createCheckInEvent = async (
  db: Database,
  teamId: string,
  userId: string,
  data: any,
) => {
  const [event] = await db
    .insert(checkInEvents)
    .values({
      ...data,
      teamId,
      createdBy: userId,
    })
    .returning();

  return event;
};

export const getEventCheckIns = getCheckIns;
export const bulkCheckIn = createCheckIn;
export const getCheckInStats = async (
  db: Database,
  teamId: string,
) => {
  return { totalCheckIns: 0, todayCheckIns: 0 };
};