import type { Database } from "@api/db";
import { events } from "@api/db/schema";
import { and, desc, eq } from "drizzle-orm";

export type GetEventsParams = {
  teamId: string;
  cursor?: string | null;
  pageSize?: number;
  q?: string | null;
  sort?: string[] | null;
};

export const getEvents = async (
  db: Database,
  params: GetEventsParams,
) => {
  const { teamId, pageSize = 25 } = params;

  const result = await db
    .select()
    .from(events)
    .where(eq(events.teamId, teamId))
    .orderBy(desc(events.createdAt))
    .limit(pageSize);

  return { data: result };
};

export const getEventById = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  const [event] = await db
    .select()
    .from(events)
    .where(and(eq(events.id, id), eq(events.teamId, teamId)));

  return event;
};

export const createEvent = async (
  db: Database,
  teamId: string,
  userId: string,
  data: any,
) => {
  const [event] = await db
    .insert(events)
    .values({
      ...data,
      teamId,
      createdBy: userId,
    })
    .returning();

  return event;
};

export const updateEvent = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  const [event] = await db
    .update(events)
    .set(data)
    .where(and(eq(events.id, data.id), eq(events.teamId, teamId)))
    .returning();

  return event;
};

export const deleteEvent = async (
  db: Database,
  teamId: string,
  id: string,
) => {
  await db
    .delete(events)
    .where(and(eq(events.id, id), eq(events.teamId, teamId)));

  return { success: true };
};

export const getEventRegistrations = async (
  db: Database,
  teamId: string,
  eventId: string,
) => {
  // Return empty array for now since we don't have registration tables set up
  return [];
};

export const createEventRegistration = async (
  db: Database,
  teamId: string,
  data: any,
) => {
  // Return mock response for now
  return { success: true };
};