/**
 * Generate a random confirmation code for event registrations
 * Format: 3 letters followed by 3 numbers (e.g., ABC123)
 */
export function generateConfirmationCode(): string {
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  
  let result = '';
  
  // Generate 3 random letters
  for (let i = 0; i < 3; i++) {
    result += letters.charAt(Math.floor(Math.random() * letters.length));
  }
  
  // Generate 3 random numbers
  for (let i = 0; i < 3; i++) {
    result += numbers.charAt(Math.floor(Math.random() * numbers.length));
  }
  
  return result;
}

/**
 * Generate a security code for child check-in (4 digits)
 */
export function generateSecurityCode(): string {
  return Math.floor(1000 + Math.random() * 9000).toString();
}

/**
 * Generate a QR code content for event check-in
 */
export function generateCheckInQRCode(eventId: string, registrationId: string): string {
  return `${process.env.FAITHOS_APP_URL}/check-in/${eventId}/${registrationId}`;
}

/**
 * Generate a unique badge ID for printing
 */
export function generateBadgeId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 7);
  return `${timestamp}${random}`.toUpperCase();
}