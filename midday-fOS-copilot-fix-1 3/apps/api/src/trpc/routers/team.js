"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.teamRouter = void 0;
var bank_connections_1 = require("@api/db/queries/bank-connections");
var teams_1 = require("@api/db/queries/teams");
var user_invites_1 = require("@api/db/queries/user-invites");
var users_on_team_1 = require("@api/db/queries/users-on-team");
var team_1 = require("@api/schemas/team");
var init_1 = require("@api/trpc/init");
var v3_1 = require("@trigger.dev/sdk/v3");
var server_1 = require("@trpc/server");
exports.teamRouter = (0, init_1.createTRPCRouter)({
    current: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, teams_1.getTeamById)(db, teamId)];
        });
    }); }),
    update: init_1.protectedProcedure
        .input(team_1.updateTeamByIdSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, teamId = _c.teamId, input = _b.input;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, teams_1.updateTeamById)(db, {
                    id: teamId,
                    data: input,
                })];
        });
    }); }),
    members: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, users_on_team_1.getTeamMembers)(db, teamId)];
        });
    }); }),
    list: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, session = _c.session;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, users_on_team_1.getTeamsByUserId)(db, session.user.id)];
        });
    }); }),
    create: init_1.protectedProcedure
        .input(team_1.createTeamSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, session = _c.session, input = _b.input;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, teams_1.createTeam)(db, __assign(__assign({}, input), { userId: session.user.id }))];
        });
    }); }),
    leave: init_1.protectedProcedure
        .input(team_1.leaveTeamSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var teamMembersData, currentUser, totalOwners;
        var _c = _b.ctx, db = _c.db, session = _c.session, input = _b.input;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0: return [4 /*yield*/, (0, users_on_team_1.getTeamMembers)(db, input.teamId)];
                case 1:
                    teamMembersData = _d.sent();
                    currentUser = teamMembersData === null || teamMembersData === void 0 ? void 0 : teamMembersData.find(function (member) { var _a; return ((_a = member.user) === null || _a === void 0 ? void 0 : _a.id) === session.user.id; });
                    totalOwners = teamMembersData === null || teamMembersData === void 0 ? void 0 : teamMembersData.filter(function (member) { return member.role === "owner"; }).length;
                    if ((currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === "owner" && totalOwners === 1) {
                        throw Error("Action not allowed");
                    }
                    return [2 /*return*/, (0, teams_1.leaveTeam)(db, {
                            userId: session.user.id,
                            teamId: input.teamId,
                        })];
            }
        });
    }); }),
    acceptInvite: init_1.protectedProcedure
        .input(team_1.acceptTeamInviteSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, session = _c.session, input = _b.input;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, user_invites_1.acceptTeamInvite)(db, {
                    id: input.id,
                    userId: session.user.id,
                })];
        });
    }); }),
    declineInvite: init_1.protectedProcedure
        .input(team_1.declineTeamInviteSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, session = _c.session, input = _b.input;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, user_invites_1.declineTeamInvite)(db, {
                    id: input.id,
                    email: session.user.email,
                })];
        });
    }); }),
    delete: init_1.protectedProcedure
        .input(team_1.deleteTeamSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var data, bankConnections;
        var db = _b.ctx.db, input = _b.input;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0: return [4 /*yield*/, (0, teams_1.deleteTeam)(db, input.teamId)];
                case 1:
                    data = _c.sent();
                    if (!data) {
                        throw new server_1.TRPCError({
                            code: "INTERNAL_SERVER_ERROR",
                            message: "Team not found",
                        });
                    }
                    return [4 /*yield*/, (0, bank_connections_1.getBankConnections)(db, {
                            teamId: data.id,
                        })];
                case 2:
                    bankConnections = _c.sent();
                    if (!(bankConnections.length > 0)) return [3 /*break*/, 4];
                    return [4 /*yield*/, v3_1.tasks.trigger("delete-team", {
                            teamId: input.teamId,
                            connections: bankConnections.map(function (connection) { return ({
                                accessToken: connection.accessToken,
                                provider: connection.provider,
                                referenceId: connection.referenceId,
                            }); }),
                        })];
                case 3:
                    _c.sent();
                    _c.label = 4;
                case 4: return [2 /*return*/];
            }
        });
    }); }),
    deleteMember: init_1.protectedProcedure
        .input(team_1.deleteTeamMemberSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var db = _b.ctx.db, input = _b.input;
        return __generator(this, function (_c) {
            return [2 /*return*/, (0, teams_1.deleteTeamMember)(db, {
                    teamId: input.teamId,
                    userId: input.userId,
                })];
        });
    }); }),
    updateMember: init_1.protectedProcedure
        .input(team_1.updateTeamMemberSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var db = _b.ctx.db, input = _b.input;
        return __generator(this, function (_c) {
            return [2 /*return*/, (0, teams_1.updateTeamMember)(db, input)];
        });
    }); }),
    teamInvites: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, user_invites_1.getTeamInvites)(db, teamId)];
        });
    }); }),
    invitesByEmail: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, session = _c.session;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, user_invites_1.getInvitesByEmail)(db, session.user.email)];
        });
    }); }),
    invite: init_1.protectedProcedure
        .input(team_1.inviteTeamMembersSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var ip, data, invites;
        var _c, _d;
        var _e = _b.ctx, db = _e.db, session = _e.session, teamId = _e.teamId, geo = _e.geo, input = _b.input;
        return __generator(this, function (_f) {
            switch (_f.label) {
                case 0:
                    ip = (_c = geo.ip) !== null && _c !== void 0 ? _c : "127.0.0.1";
                    return [4 /*yield*/, (0, user_invites_1.createTeamInvites)(db, {
                            teamId: teamId,
                            invites: input.map(function (invite) { return (__assign(__assign({}, invite), { invitedBy: session.user.id })); }),
                        })];
                case 1:
                    data = _f.sent();
                    invites = (_d = data === null || data === void 0 ? void 0 : data.map(function (invite) {
                        var _a;
                        return ({
                            email: invite === null || invite === void 0 ? void 0 : invite.email,
                            invitedBy: session.user.id,
                            invitedByName: session.user.full_name,
                            invitedByEmail: session.user.email,
                            teamName: (_a = invite === null || invite === void 0 ? void 0 : invite.team) === null || _a === void 0 ? void 0 : _a.name,
                            inviteCode: invite === null || invite === void 0 ? void 0 : invite.code,
                        });
                    })) !== null && _d !== void 0 ? _d : [];
                    return [4 /*yield*/, v3_1.tasks.trigger("invite-team-members", {
                            teamId: teamId,
                            invites: invites,
                            ip: ip,
                            locale: "en",
                        })];
                case 2:
                    _f.sent();
                    return [2 /*return*/];
            }
        });
    }); }),
    deleteInvite: init_1.protectedProcedure
        .input(team_1.deleteTeamInviteSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, teamId = _c.teamId, input = _b.input;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, user_invites_1.deleteTeamInvite)(db, {
                    teamId: teamId,
                    id: input.id,
                })];
        });
    }); }),
    availablePlans: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, teams_1.getAvailablePlans)(db, teamId)];
        });
    }); }),
    updateBaseCurrency: init_1.protectedProcedure
        .input(team_1.updateBaseCurrencySchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var event;
        var teamId = _b.ctx.teamId, input = _b.input;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0: return [4 /*yield*/, v3_1.tasks.trigger("update-base-currency", {
                        teamId: teamId,
                        baseCurrency: input.baseCurrency,
                    })];
                case 1:
                    event = _c.sent();
                    return [2 /*return*/, event];
            }
        });
    }); }),
});
