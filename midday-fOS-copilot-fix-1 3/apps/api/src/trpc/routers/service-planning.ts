import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../init";
import {
  getServicePlans,
  getServicePlanById,
  createServicePlan,
  updateServicePlan,
  deleteServicePlan,
  getServiceRoles,
  assignServiceRole,
  removeServiceRole,
  getServiceSongs,
  addServiceSong,
  removeServiceSong,
} from "@api/db/queries/service-planning";

export const servicePlanningRouter = createTRPCRouter({
  // Get all service plans for team
  list: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        pageSize: z.number().min(1).max(100).default(25),
        q: z.string().optional(),
        sort: z.array(z.string()).optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        serviceType: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      return getServicePlans(ctx.db, {
        teamId: ctx.teamId,
        ...input,
      });
    }),

  // Get single service plan by ID
  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return getServicePlanById(ctx.db, ctx.teamId, input.id);
    }),

  // Create new service plan
  create: protectedProcedure
    .input(
      z.object({
        title: z.string().min(1),
        serviceDate: z.string(),
        serviceTime: z.string(),
        serviceType: z.string(),
        theme: z.string().optional(),
        notes: z.string().optional(),
        status: z.string().default("draft"),
        orderOfService: z.array(z.record(z.any())).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return createServicePlan(ctx.db, ctx.teamId, ctx.session.user.id, input);
    }),

  // Update existing service plan
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        title: z.string().optional(),
        serviceDate: z.string().optional(),
        serviceTime: z.string().optional(),
        serviceType: z.string().optional(),
        theme: z.string().optional(),
        notes: z.string().optional(),
        status: z.string().optional(),
        orderOfService: z.array(z.record(z.any())).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return updateServicePlan(ctx.db, ctx.teamId, input);
    }),

  // Delete service plan
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return deleteServicePlan(ctx.db, ctx.teamId, input.id);
    }),

  // Get service roles for a plan
  roles: protectedProcedure
    .input(z.object({ servicePlanId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getServiceRoles(ctx.db, ctx.teamId, input.servicePlanId);
    }),

  // Assign person to service role
  assignRole: protectedProcedure
    .input(
      z.object({
        servicePlanId: z.string(),
        personId: z.string(),
        roleType: z.string(),
        notes: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return assignServiceRole(ctx.db, ctx.teamId, input);
    }),

  // Remove person from service role
  removeRole: protectedProcedure
    .input(
      z.object({
        servicePlanId: z.string(),
        personId: z.string(),
        roleType: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return removeServiceRole(ctx.db, ctx.teamId, input);
    }),

  // Get songs for a service plan
  songs: protectedProcedure
    .input(z.object({ servicePlanId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getServiceSongs(ctx.db, ctx.teamId, input.servicePlanId);
    }),

  // Add song to service plan
  addSong: protectedProcedure
    .input(
      z.object({
        servicePlanId: z.string(),
        songId: z.string(),
        orderIndex: z.number(),
        key: z.string().optional(),
        tempo: z.string().optional(),
        notes: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return addServiceSong(ctx.db, ctx.teamId, input);
    }),

  // Remove song from service plan
  removeSong: protectedProcedure
    .input(
      z.object({
        servicePlanId: z.string(),
        songId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return removeServiceSong(ctx.db, ctx.teamId, input);
    }),
});