import { z } from "zod";
import { createTR<PERSON><PERSON>outer, protectedProcedure } from "../init";
import {
  getP<PERSON><PERSON>,
  getPersonById,
  create<PERSON>erson,
  update<PERSON>erson,
  deletePerson,
  getPersonGroups,
  getPersonEvents,
} from "@api/db/queries/people";

export const peopleRouter = createTRPCRouter({
  // Get all people for team
  list: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        pageSize: z.number().min(1).max(100).default(25),
        q: z.string().optional(),
        sort: z.array(z.string()).optional(),
        membershipStatus: z.string().optional(),
        status: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      return getPeople(ctx.db, {
        teamId: ctx.teamId,
        ...input,
      });
    }),

  // Get single person by ID
  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return getPersonById(ctx.db, ctx.teamId, input.id);
    }),

  // Create new person
  create: protectedProcedure
    .input(
      z.object({
        firstName: z.string().min(1),
        lastName: z.string().min(1),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        dateOfBirth: z.string().optional(),
        gender: z.string().optional(),
        maritalStatus: z.string().optional(),
        membershipStatus: z.string().default("visitor"),
        status: z.string().default("active"),
        joinDate: z.string().optional(),
        address: z.record(z.any()).optional(),
        emergencyContact: z.record(z.any()).optional(),
        notes: z.string().optional(),
        tags: z.array(z.string()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return createPerson(ctx.db, ctx.teamId, ctx.session.user.id, input);
    }),

  // Update existing person
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        dateOfBirth: z.string().optional(),
        gender: z.string().optional(),
        maritalStatus: z.string().optional(),
        membershipStatus: z.string().optional(),
        status: z.string().optional(),
        joinDate: z.string().optional(),
        address: z.record(z.any()).optional(),
        emergencyContact: z.record(z.any()).optional(),
        notes: z.string().optional(),
        tags: z.array(z.string()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return updatePerson(ctx.db, ctx.teamId, input);
    }),

  // Delete person
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return deletePerson(ctx.db, ctx.teamId, input.id);
    }),

  // Get person's groups
  groups: protectedProcedure
    .input(z.object({ personId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getPersonGroups(ctx.db, ctx.teamId, input.personId);
    }),

  // Get person's events
  events: protectedProcedure
    .input(z.object({ personId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getPersonEvents(ctx.db, ctx.teamId, input.personId);
    }),
});