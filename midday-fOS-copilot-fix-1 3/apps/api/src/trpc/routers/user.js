"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRouter = void 0;
var user_invites_1 = require("@api/db/queries/user-invites");
var users_1 = require("@api/db/queries/users");
var users_2 = require("@api/schemas/users");
var resend_1 = require("@api/services/resend");
var init_1 = require("@api/trpc/init");
var zod_1 = require("zod");
var widgetConfigSchema = zod_1.z.object({
    enabledWidgets: zod_1.z.array(zod_1.z.string()).optional(),
    widgetOrder: zod_1.z.array(zod_1.z.string()).optional(),
    widgetSettings: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional(),
});
exports.userRouter = (0, init_1.createTRPCRouter)({
    me: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, session = _c.session;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, users_1.getUserById)(db, session.user.id)];
        });
    }); }),
    update: init_1.protectedProcedure
        .input(users_2.updateUserSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, session = _c.session, input = _b.input;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, users_1.updateUser)(db, __assign({ id: session.user.id }, input))];
        });
    }); }),
    delete: init_1.protectedProcedure.mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var data;
        var _c = _b.ctx, supabase = _c.supabase, db = _c.db, session = _c.session;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0: return [4 /*yield*/, Promise.all([
                        (0, users_1.deleteUser)(db, session.user.id),
                        supabase.auth.admin.deleteUser(session.user.id),
                        resend_1.resend.contacts.remove({
                            email: session.user.email,
                            audienceId: process.env.RESEND_AUDIENCE_ID,
                        }),
                    ])];
                case 1:
                    data = (_d.sent())[0];
                    return [2 /*return*/, data];
            }
        });
    }); }),
    invites: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, session = _c.session;
        return __generator(this, function (_d) {
            if (!session.user.email) {
                return [2 /*return*/, []];
            }
            return [2 /*return*/, (0, user_invites_1.getUserInvites)(db, session.user.email)];
        });
    }); }),
    updateWidgetConfig: init_1.protectedProcedure
        .input(widgetConfigSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, session = _c.session, input = _b.input;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, users_1.updateUser)(db, {
                    id: session.user.id,
                    widgetConfig: input,
                })];
        });
    }); }),
    getWidgetConfig: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var user;
        var _c = _b.ctx, db = _c.db, session = _c.session;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0: return [4 /*yield*/, (0, users_1.getUserById)(db, session.user.id)];
                case 1:
                    user = _d.sent();
                    return [2 /*return*/, (user === null || user === void 0 ? void 0 : user.widgetConfig) || null];
            }
        });
    }); }),
});
