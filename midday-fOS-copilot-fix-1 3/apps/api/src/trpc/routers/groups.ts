import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../init";
import {
  getGroups,
  getGroupById,
  createGroup,
  updateGroup,
  deleteGroup,
  getGroupMembers,
  addGroupMember,
  removeGroupMember,
  getGroupEvents,
} from "@api/db/queries/groups";

export const groupsRouter = createTRPCRouter({
  // Get all groups for team
  list: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        pageSize: z.number().min(1).max(100).default(25),
        q: z.string().optional(),
        sort: z.array(z.string()).optional(),
        groupType: z.string().optional(),
        status: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      return getGroups(ctx.db, {
        teamId: ctx.teamId,
        ...input,
      });
    }),

  // Get single group by ID
  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return getGroupById(ctx.db, ctx.teamId, input.id);
    }),

  // Create new group
  create: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1),
        description: z.string().optional(),
        groupType: z.string(),
        status: z.string().default("active"),
        meetingSchedule: z.record(z.any()).optional(),
        location: z.string().optional(),
        maxMembers: z.number().optional(),
        isPublic: z.boolean().default(true),
        tags: z.array(z.string()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return createGroup(ctx.db, ctx.teamId, ctx.session.user.id, input);
    }),

  // Update existing group
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        description: z.string().optional(),
        groupType: z.string().optional(),
        status: z.string().optional(),
        meetingSchedule: z.record(z.any()).optional(),
        location: z.string().optional(),
        maxMembers: z.number().optional(),
        isPublic: z.boolean().optional(),
        tags: z.array(z.string()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return updateGroup(ctx.db, ctx.teamId, input);
    }),

  // Delete group
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return deleteGroup(ctx.db, ctx.teamId, input.id);
    }),

  // Get group members
  members: protectedProcedure
    .input(z.object({ groupId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getGroupMembers(ctx.db, ctx.teamId, input.groupId);
    }),

  // Add member to group
  addMember: protectedProcedure
    .input(
      z.object({
        groupId: z.string(),
        personId: z.string(),
        role: z.string().default("member"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return addGroupMember(ctx.db, ctx.teamId, input);
    }),

  // Remove member from group
  removeMember: protectedProcedure
    .input(
      z.object({
        groupId: z.string(),
        personId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return removeGroupMember(ctx.db, ctx.teamId, input);
    }),

  // Get group events
  events: protectedProcedure
    .input(z.object({ groupId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getGroupEvents(ctx.db, ctx.teamId, input.groupId);
    }),
});