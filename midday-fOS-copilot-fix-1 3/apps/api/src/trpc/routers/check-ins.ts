import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../init";
import {
  getCheckIns,
  getCheckInById,
  createCheckIn,
  updateCheckIn,
  deleteCheckIn,
  getEventCheckIns,
  bulkCheckIn,
  getCheckInStats,
} from "@api/db/queries/check-ins";

export const checkInsRouter = createTRPCRouter({
  // Get all check-ins for team
  list: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        pageSize: z.number().min(1).max(100).default(25),
        q: z.string().optional(),
        sort: z.array(z.string()).optional(),
        eventId: z.string().optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        status: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      return getCheckIns(ctx.db, {
        teamId: ctx.teamId,
        ...input,
      });
    }),

  // Get single check-in by ID
  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return getCheckInById(ctx.db, ctx.teamId, input.id);
    }),

  // Create new check-in
  create: protectedProcedure
    .input(
      z.object({
        eventId: z.string(),
        personId: z.string(),
        checkInTime: z.string(),
        checkOutTime: z.string().optional(),
        status: z.string().default("checked_in"),
        notes: z.string().optional(),
        checkedInBy: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return createCheckIn(ctx.db, ctx.teamId, {
        ...input,
        checkedInBy: input.checkedInBy || ctx.session.user.id,
      });
    }),

  // Update existing check-in
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        checkInTime: z.string().optional(),
        checkOutTime: z.string().optional(),
        status: z.string().optional(),
        notes: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return updateCheckIn(ctx.db, ctx.teamId, input);
    }),

  // Delete check-in
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return deleteCheckIn(ctx.db, ctx.teamId, input.id);
    }),

  // Get check-ins for a specific event
  byEvent: protectedProcedure
    .input(z.object({ eventId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getEventCheckIns(ctx.db, ctx.teamId, input.eventId);
    }),

  // Bulk check-in multiple people
  bulkCheckIn: protectedProcedure
    .input(
      z.object({
        eventId: z.string(),
        personIds: z.array(z.string()),
        checkInTime: z.string(),
        notes: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return bulkCheckIn(ctx.db, ctx.teamId, {
        ...input,
        checkedInBy: ctx.session.user.id,
      });
    }),

  // Get check-in statistics
  stats: protectedProcedure
    .input(
      z.object({
        eventId: z.string().optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      return getCheckInStats(ctx.db, ctx.teamId, input);
    }),
});