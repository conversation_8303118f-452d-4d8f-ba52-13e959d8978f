"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.peopleRouter = void 0;
var zod_1 = require("zod");
var init_1 = require("../init");
var people_1 = require("@api/db/queries/people");
exports.peopleRouter = (0, init_1.createTRPCRouter)({
    // Get all people for team
    list: init_1.protectedProcedure
        .input(zod_1.z.object({
        cursor: zod_1.z.string().optional(),
        pageSize: zod_1.z.number().min(1).max(100).default(25),
        q: zod_1.z.string().optional(),
        sort: zod_1.z.array(zod_1.z.string()).optional(),
        membershipStatus: zod_1.z.string().optional(),
        status: zod_1.z.string().optional(),
    }))
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var ctx = _b.ctx, input = _b.input;
        return __generator(this, function (_c) {
            return [2 /*return*/, (0, people_1.getPeople)(ctx.db, __assign({ teamId: ctx.teamId }, input))];
        });
    }); }),
    // Get single person by ID
    get: init_1.protectedProcedure
        .input(zod_1.z.object({ id: zod_1.z.string() }))
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var ctx = _b.ctx, input = _b.input;
        return __generator(this, function (_c) {
            return [2 /*return*/, (0, people_1.getPersonById)(ctx.db, ctx.teamId, input.id)];
        });
    }); }),
    // Create new person
    create: init_1.protectedProcedure
        .input(zod_1.z.object({
        firstName: zod_1.z.string().min(1),
        lastName: zod_1.z.string().min(1),
        email: zod_1.z.string().email().optional(),
        phone: zod_1.z.string().optional(),
        dateOfBirth: zod_1.z.string().optional(),
        gender: zod_1.z.string().optional(),
        maritalStatus: zod_1.z.string().optional(),
        membershipStatus: zod_1.z.string().default("visitor"),
        status: zod_1.z.string().default("active"),
        joinDate: zod_1.z.string().optional(),
        address: zod_1.z.record(zod_1.z.any()).optional(),
        emergencyContact: zod_1.z.record(zod_1.z.any()).optional(),
        notes: zod_1.z.string().optional(),
        tags: zod_1.z.array(zod_1.z.string()).optional(),
    }))
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var ctx = _b.ctx, input = _b.input;
        return __generator(this, function (_c) {
            return [2 /*return*/, (0, people_1.createPerson)(ctx.db, ctx.teamId, ctx.session.user.id, input)];
        });
    }); }),
    // Update existing person
    update: init_1.protectedProcedure
        .input(zod_1.z.object({
        id: zod_1.z.string(),
        firstName: zod_1.z.string().optional(),
        lastName: zod_1.z.string().optional(),
        email: zod_1.z.string().email().optional(),
        phone: zod_1.z.string().optional(),
        dateOfBirth: zod_1.z.string().optional(),
        gender: zod_1.z.string().optional(),
        maritalStatus: zod_1.z.string().optional(),
        membershipStatus: zod_1.z.string().optional(),
        status: zod_1.z.string().optional(),
        joinDate: zod_1.z.string().optional(),
        address: zod_1.z.record(zod_1.z.any()).optional(),
        emergencyContact: zod_1.z.record(zod_1.z.any()).optional(),
        notes: zod_1.z.string().optional(),
        tags: zod_1.z.array(zod_1.z.string()).optional(),
    }))
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var ctx = _b.ctx, input = _b.input;
        return __generator(this, function (_c) {
            return [2 /*return*/, (0, people_1.updatePerson)(ctx.db, ctx.teamId, input)];
        });
    }); }),
    // Delete person
    delete: init_1.protectedProcedure
        .input(zod_1.z.object({ id: zod_1.z.string() }))
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var ctx = _b.ctx, input = _b.input;
        return __generator(this, function (_c) {
            return [2 /*return*/, (0, people_1.deletePerson)(ctx.db, ctx.teamId, input.id)];
        });
    }); }),
    // Get person's groups
    groups: init_1.protectedProcedure
        .input(zod_1.z.object({ personId: zod_1.z.string() }))
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var ctx = _b.ctx, input = _b.input;
        return __generator(this, function (_c) {
            return [2 /*return*/, (0, people_1.getPersonGroups)(ctx.db, ctx.teamId, input.personId)];
        });
    }); }),
    // Get person's events
    events: init_1.protectedProcedure
        .input(zod_1.z.object({ personId: zod_1.z.string() }))
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var ctx = _b.ctx, input = _b.input;
        return __generator(this, function (_c) {
            return [2 /*return*/, (0, people_1.getPersonEvents)(ctx.db, ctx.teamId, input.personId)];
        });
    }); }),
});
