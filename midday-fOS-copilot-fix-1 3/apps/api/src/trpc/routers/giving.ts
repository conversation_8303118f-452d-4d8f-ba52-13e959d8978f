import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../init";
import {
  getDonations,
  getDonationById,
  createDonation,
  updateDonation,
  deleteDonation,
  getDonationStats,
  getDonationsByPerson,
  createGivingCampaign,
  getGivingCampaigns,
} from "@api/db/queries/giving";

export const givingRouter = createTRPCRouter({
  // Get all donations for team
  list: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        pageSize: z.number().min(1).max(100).default(25),
        q: z.string().optional(),
        sort: z.array(z.string()).optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        donationType: z.string().optional(),
        fundId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      return getDonations(ctx.db, {
        teamId: ctx.teamId,
        ...input,
      });
    }),

  // Get single donation by ID
  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return getDonationById(ctx.db, ctx.teamId, input.id);
    }),

  // Create new donation
  create: protectedProcedure
    .input(
      z.object({
        personId: z.string().optional(),
        amount: z.number().positive(),
        donationType: z.string(),
        fundId: z.string().optional(),
        paymentMethod: z.string(),
        donationDate: z.string(),
        notes: z.string().optional(),
        isAnonymous: z.boolean().default(false),
        recurringSchedule: z.record(z.any()).optional(),
        taxDeductible: z.boolean().default(true),
        campaignId: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return createDonation(ctx.db, ctx.teamId, ctx.session.user.id, input);
    }),

  // Update existing donation
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        personId: z.string().optional(),
        amount: z.number().positive().optional(),
        donationType: z.string().optional(),
        fundId: z.string().optional(),
        paymentMethod: z.string().optional(),
        donationDate: z.string().optional(),
        notes: z.string().optional(),
        isAnonymous: z.boolean().optional(),
        recurringSchedule: z.record(z.any()).optional(),
        taxDeductible: z.boolean().optional(),
        campaignId: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return updateDonation(ctx.db, ctx.teamId, input);
    }),

  // Delete donation
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return deleteDonation(ctx.db, ctx.teamId, input.id);
    }),

  // Get donation statistics
  stats: protectedProcedure
    .input(
      z.object({
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        fundId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      return getDonationStats(ctx.db, ctx.teamId, input);
    }),

  // Get donations by person
  byPerson: protectedProcedure
    .input(z.object({ personId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getDonationsByPerson(ctx.db, ctx.teamId, input.personId);
    }),

  // Get giving campaigns
  campaigns: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        pageSize: z.number().min(1).max(100).default(25),
        status: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      return getGivingCampaigns(ctx.db, {
        teamId: ctx.teamId,
        ...input,
      });
    }),

  // Create giving campaign
  createCampaign: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1),
        description: z.string().optional(),
        goalAmount: z.number().positive(),
        startDate: z.string(),
        endDate: z.string(),
        status: z.string().default("active"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return createGivingCampaign(ctx.db, ctx.teamId, ctx.session.user.id, input);
    }),
});