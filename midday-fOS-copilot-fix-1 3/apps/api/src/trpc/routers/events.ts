import { z } from "zod";
import { and, desc, eq } from "drizzle-orm";
import { createTRPCRouter, protectedProcedure } from "../init";
import { events } from "@/db/schema";
import {
  createEvent,
  updateEvent,
  deleteEvent,
  getEvents,
  getEventById,
  getEventRegistrations,
  createEventRegistration,
} from "@api/db/queries/events";

export const eventsRouter = createTRPCRouter({
  // Get all events for team
  list: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        pageSize: z.number().min(1).max(100).default(25),
        q: z.string().optional(),
        sort: z.array(z.string()).optional(),
        status: z.string().optional(),
        type: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      return getEvents(ctx.db, {
        teamId: ctx.teamId,
        ...input,
      });
    }),

  // Get single event by ID
  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return getEventById(ctx.db, ctx.teamId, input.id);
    }),

  // Create new event
  create: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1),
        description: z.string().optional(),
        eventType: z.string(),
        status: z.string().default("draft"),
        startDate: z.string(),
        endDate: z.string().optional(),
        location: z.string().optional(),
        maxAttendees: z.number().optional(),
        registrationEnabled: z.boolean().default(false),
        registrationDeadline: z.string().optional(),
        cost: z.number().optional(),
        tags: z.array(z.string()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return createEvent(ctx.db, ctx.teamId, ctx.session.user.id, input);
    }),

  // Update existing event
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        description: z.string().optional(),
        eventType: z.string().optional(),
        status: z.string().optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        location: z.string().optional(),
        maxAttendees: z.number().optional(),
        registrationEnabled: z.boolean().optional(),
        registrationDeadline: z.string().optional(),
        cost: z.number().optional(),
        tags: z.array(z.string()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return updateEvent(ctx.db, ctx.teamId, input);
    }),

  // Delete event
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return deleteEvent(ctx.db, ctx.teamId, input.id);
    }),

  // Get event registrations
  registrations: protectedProcedure
    .input(z.object({ eventId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getEventRegistrations(ctx.db, ctx.teamId, input.eventId);
    }),

  // Create event registration
  register: protectedProcedure
    .input(
      z.object({
        eventId: z.string(),
        personId: z.string(),
        status: z.string().default("registered"),
        registrationData: z.record(z.any()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return createEventRegistration(ctx.db, ctx.teamId, input);
    }),
});