"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.appRouter = void 0;
var init_1 = require("../init");
var api_keys_1 = require("./api-keys");
var apps_1 = require("./apps");
var bank_accounts_1 = require("./bank-accounts");
var bank_connections_1 = require("./bank-connections");
var customers_1 = require("./customers");
var document_tag_assignments_1 = require("./document-tag-assignments");
var document_tags_1 = require("./document-tags");
var documents_1 = require("./documents");
var inbox_1 = require("./inbox");
var inbox_accounts_1 = require("./inbox-accounts");
var institutions_1 = require("./institutions");
var invoice_1 = require("./invoice");
var invoice_template_1 = require("./invoice-template");
var metrics_1 = require("./metrics");
var search_1 = require("./search");
var tags_1 = require("./tags");
var team_1 = require("./team");
var tracker_entries_1 = require("./tracker-entries");
var tracker_projects_1 = require("./tracker-projects");
var transaction_attachments_1 = require("./transaction-attachments");
var transaction_categories_1 = require("./transaction-categories");
var transaction_tags_1 = require("./transaction-tags");
var transactions_1 = require("./transactions");
var user_1 = require("./user");
// Church management routers
var events_1 = require("./events");
var people_1 = require("./people");
var groups_1 = require("./groups");
var giving_1 = require("./giving");
var service_planning_1 = require("./service-planning");
var check_ins_1 = require("./check-ins");
exports.appRouter = (0, init_1.createTRPCRouter)({
    apps: apps_1.appsRouter,
    bankAccounts: bank_accounts_1.bankAccountsRouter,
    bankConnections: bank_connections_1.bankConnectionsRouter,
    customers: customers_1.customersRouter,
    documents: documents_1.documentsRouter,
    documentTagAssignments: document_tag_assignments_1.documentTagAssignmentsRouter,
    documentTags: document_tags_1.documentTagsRouter,
    inbox: inbox_1.inboxRouter,
    inboxAccounts: inbox_accounts_1.inboxAccountsRouter,
    institutions: institutions_1.institutionsRouter,
    invoice: invoice_1.invoiceRouter,
    invoiceTemplate: invoice_template_1.invoiceTemplateRouter,
    metrics: metrics_1.metricsRouter,
    tags: tags_1.tagsRouter,
    team: team_1.teamRouter,
    trackerEntries: tracker_entries_1.trackerEntriesRouter,
    trackerProjects: tracker_projects_1.trackerProjectsRouter,
    transactionAttachments: transaction_attachments_1.transactionAttachmentsRouter,
    transactionCategories: transaction_categories_1.transactionCategoriesRouter,
    transactions: transactions_1.transactionsRouter,
    transactionTags: transaction_tags_1.transactionTagsRouter,
    user: user_1.userRouter,
    search: search_1.searchRouter,
    apiKeys: api_keys_1.apiKeysRouter,
    // Church management routers
    events: events_1.eventsRouter,
    people: people_1.peopleRouter,
    groups: groups_1.groupsRouter,
    giving: giving_1.givingRouter,
    servicePlanning: service_planning_1.servicePlanningRouter,
    checkIns: check_ins_1.checkInsRouter,
});
