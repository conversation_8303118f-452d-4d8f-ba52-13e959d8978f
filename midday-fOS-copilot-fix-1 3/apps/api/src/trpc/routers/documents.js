"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.documentsRouter = void 0;
var documents_1 = require("@api/db/queries/documents");
var documents_2 = require("@api/schemas/documents");
var init_1 = require("@api/trpc/init");
var utils_1 = require("@faithos/documents/utils");
var storage_1 = require("@faithos/supabase/storage");
var v3_1 = require("@trigger.dev/sdk/v3");
var server_1 = require("@trpc/server");
exports.documentsRouter = (0, init_1.createTRPCRouter)({
    get: init_1.protectedProcedure
        .input(documents_2.getDocumentsSchema)
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, documents_1.getDocuments)(db, __assign({ teamId: teamId }, input))];
        });
    }); }),
    getById: init_1.protectedProcedure
        .input(documents_2.getDocumentSchema)
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, documents_1.getDocumentById)(db, {
                    id: input.id,
                    filePath: input.filePath,
                    teamId: teamId,
                })];
        });
    }); }),
    getRelatedDocuments: init_1.protectedProcedure
        .input(documents_2.getRelatedDocumentsSchema)
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, documents_1.getRelatedDocuments)(db, {
                    id: input.id,
                    pageSize: input.pageSize,
                    teamId: teamId,
                })];
        });
    }); }),
    delete: init_1.protectedProcedure
        .input(documents_2.deleteDocumentSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var document;
        var input = _b.input, _c = _b.ctx, db = _c.db, supabase = _c.supabase, teamId = _c.teamId;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0: return [4 /*yield*/, (0, documents_1.deleteDocument)(db, {
                        id: input.id,
                        teamId: teamId,
                    })];
                case 1:
                    document = _d.sent();
                    if (!document || !document.pathTokens) {
                        throw new server_1.TRPCError({
                            code: "NOT_FOUND",
                            message: "Document not found",
                        });
                    }
                    // Delete from storage
                    return [4 /*yield*/, (0, storage_1.remove)(supabase, {
                            bucket: "vault",
                            path: document.pathTokens,
                        })];
                case 2:
                    // Delete from storage
                    _d.sent();
                    return [2 /*return*/, document];
            }
        });
    }); }),
    processDocument: init_1.protectedProcedure
        .input(documents_2.processDocumentSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var supportedDocuments, unsupportedDocuments, unsupportedNames;
        var _c = _b.ctx, teamId = _c.teamId, db = _c.db, input = _b.input;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    supportedDocuments = input.filter(function (item) {
                        return (0, utils_1.isMimeTypeSupportedForProcessing)(item.mimetype);
                    });
                    unsupportedDocuments = input.filter(function (item) { return !(0, utils_1.isMimeTypeSupportedForProcessing)(item.mimetype); });
                    if (!(unsupportedDocuments.length > 0)) return [3 /*break*/, 2];
                    unsupportedNames = unsupportedDocuments.map(function (doc) {
                        return doc.filePath.join("/");
                    });
                    return [4 /*yield*/, (0, documents_1.updateDocuments)(db, {
                            ids: unsupportedNames,
                            teamId: teamId,
                            processingStatus: "completed",
                        })];
                case 1:
                    _d.sent();
                    _d.label = 2;
                case 2:
                    if (supportedDocuments.length === 0) {
                        return [2 /*return*/];
                    }
                    // Trigger processing task only for supported documents
                    return [2 /*return*/, v3_1.tasks.batchTrigger("process-document", supportedDocuments.map(function (item) {
                            return ({
                                payload: {
                                    filePath: item.filePath,
                                    mimetype: item.mimetype,
                                    teamId: teamId,
                                },
                            });
                        }))];
            }
        });
    }); }),
    signedUrl: init_1.protectedProcedure
        .input(documents_2.signedUrlSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var data;
        var input = _b.input, supabase = _b.ctx.supabase;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0: return [4 /*yield*/, (0, storage_1.signedUrl)(supabase, {
                        bucket: "vault",
                        path: input.filePath,
                        expireIn: input.expireIn,
                    })];
                case 1:
                    data = (_c.sent()).data;
                    return [2 /*return*/, data];
            }
        });
    }); }),
    signedUrls: init_1.protectedProcedure
        .input(documents_2.signedUrlsSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var signedUrls, _i, input_1, filePath, data;
        var input = _b.input, supabase = _b.ctx.supabase;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    signedUrls = [];
                    _i = 0, input_1 = input;
                    _c.label = 1;
                case 1:
                    if (!(_i < input_1.length)) return [3 /*break*/, 4];
                    filePath = input_1[_i];
                    return [4 /*yield*/, (0, storage_1.signedUrl)(supabase, {
                            bucket: "vault",
                            path: filePath,
                            expireIn: 60, // 1 Minute
                        })];
                case 2:
                    data = (_c.sent()).data;
                    if (data === null || data === void 0 ? void 0 : data.signedUrl) {
                        signedUrls.push(data.signedUrl);
                    }
                    _c.label = 3;
                case 3:
                    _i++;
                    return [3 /*break*/, 1];
                case 4: return [2 /*return*/, signedUrls !== null && signedUrls !== void 0 ? signedUrls : []];
            }
        });
    }); }),
});
