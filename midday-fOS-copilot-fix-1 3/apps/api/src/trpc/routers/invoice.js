"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.invoiceRouter = void 0;
var invoice_templates_1 = require("@api/db/queries/invoice-templates");
var invoices_1 = require("@api/db/queries/invoices");
var teams_1 = require("@api/db/queries/teams");
var users_1 = require("@api/db/queries/users");
var invoice_1 = require("@api/schemas/invoice");
var init_1 = require("@api/trpc/init");
var parse_1 = require("@api/utils/parse");
var utc_1 = require("@date-fns/utc");
var token_1 = require("@faithos/invoice/token");
var v3_1 = require("@trigger.dev/sdk/v3");
var server_1 = require("@trpc/server");
var date_fns_1 = require("date-fns");
var uuid_1 = require("uuid");
var defaultTemplate = {
    title: "Invoice",
    customerLabel: "To",
    fromLabel: "From",
    invoiceNoLabel: "Invoice No",
    issueDateLabel: "Issue Date",
    dueDateLabel: "Due Date",
    descriptionLabel: "Description",
    priceLabel: "Price",
    quantityLabel: "Quantity",
    totalLabel: "Total",
    totalSummaryLabel: "Total",
    subtotalLabel: "Subtotal",
    vatLabel: "VAT",
    taxLabel: "Tax",
    paymentLabel: "Payment Details",
    paymentDetails: undefined,
    noteLabel: "Note",
    logoUrl: undefined,
    currency: "USD",
    fromDetails: undefined,
    size: "a4",
    includeVat: true,
    includeTax: true,
    discountLabel: "Discount",
    includeDiscount: false,
    includeUnits: false,
    includeDecimals: false,
    includePdf: false,
    includeQr: true,
    dateFormat: "dd/MM/yyyy",
    taxRate: 0,
    vatRate: 0,
    deliveryType: "create",
    timezone: undefined,
    locale: undefined,
};
exports.invoiceRouter = (0, init_1.createTRPCRouter)({
    get: init_1.protectedProcedure
        .input(invoice_1.getInvoicesSchema.optional())
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, invoices_1.getInvoices)(db, __assign({ teamId: teamId }, input))];
        });
    }); }),
    getById: init_1.protectedProcedure
        .input(invoice_1.getInvoiceByIdSchema)
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, invoices_1.getInvoiceById)(db, {
                    id: input.id,
                    teamId: teamId,
                })];
        });
    }); }),
    getInvoiceByToken: init_1.publicProcedure
        .input(invoice_1.getInvoiceByTokenSchema)
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var id;
        var input = _b.input, db = _b.ctx.db;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0: return [4 /*yield*/, (0, token_1.verify)(decodeURIComponent(input.token))];
                case 1:
                    id = (_c.sent()).id;
                    if (!id) {
                        throw new server_1.TRPCError({ code: "NOT_FOUND" });
                    }
                    return [2 /*return*/, (0, invoices_1.getInvoiceById)(db, {
                            id: id,
                        })];
            }
        });
    }); }),
    paymentStatus: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, invoices_1.getPaymentStatus)(db, teamId)];
        });
    }); }),
    searchInvoiceNumber: init_1.protectedProcedure
        .input(invoice_1.searchInvoiceNumberSchema)
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, invoices_1.searchInvoiceNumber)(db, {
                    teamId: teamId,
                    query: input.query,
                })];
        });
    }); }),
    invoiceSummary: init_1.protectedProcedure
        .input(invoice_1.invoiceSummarySchema.optional())
        .query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c = _b.ctx, db = _c.db, teamId = _c.teamId, input = _b.input;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, invoices_1.getInvoiceSummary)(db, {
                    teamId: teamId,
                    status: input === null || input === void 0 ? void 0 : input.status,
                })];
        });
    }); }),
    defaultSettings: init_1.protectedProcedure.query(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var _c, nextInvoiceNumber, template, team, user, locale, timezone, currency, dateFormat, logoUrl, countryCode, size, includeTax, savedTemplate;
        var _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18;
        var _19 = _b.ctx, db = _19.db, teamId = _19.teamId, session = _19.session, geo = _19.geo;
        return __generator(this, function (_20) {
            switch (_20.label) {
                case 0: return [4 /*yield*/, Promise.all([
                        (0, invoices_1.getNextInvoiceNumber)(db, teamId),
                        (0, invoice_templates_1.getInvoiceTemplate)(db, teamId),
                        (0, teams_1.getTeamById)(db, teamId),
                        (0, users_1.getUserById)(db, session === null || session === void 0 ? void 0 : session.user.id),
                    ])];
                case 1:
                    _c = _20.sent(), nextInvoiceNumber = _c[0], template = _c[1], team = _c[2], user = _c[3];
                    locale = (_e = (_d = user === null || user === void 0 ? void 0 : user.locale) !== null && _d !== void 0 ? _d : geo === null || geo === void 0 ? void 0 : geo.locale) !== null && _e !== void 0 ? _e : "en";
                    timezone = (_g = (_f = user === null || user === void 0 ? void 0 : user.timezone) !== null && _f !== void 0 ? _f : geo === null || geo === void 0 ? void 0 : geo.timezone) !== null && _g !== void 0 ? _g : "America/New_York";
                    currency = (_j = (_h = template === null || template === void 0 ? void 0 : template.currency) !== null && _h !== void 0 ? _h : team === null || team === void 0 ? void 0 : team.baseCurrency) !== null && _j !== void 0 ? _j : defaultTemplate.currency;
                    dateFormat = (_l = (_k = template === null || template === void 0 ? void 0 : template.dateFormat) !== null && _k !== void 0 ? _k : user === null || user === void 0 ? void 0 : user.dateFormat) !== null && _l !== void 0 ? _l : defaultTemplate.dateFormat;
                    logoUrl = (_m = template === null || template === void 0 ? void 0 : template.logoUrl) !== null && _m !== void 0 ? _m : defaultTemplate.logoUrl;
                    countryCode = (_o = geo.country) !== null && _o !== void 0 ? _o : "US";
                    size = ["US", "CA"].includes(countryCode) ? "letter" : "a4";
                    includeTax = ["US", "CA", "AU", "NZ", "SG", "MY", "IN"].includes(countryCode);
                    savedTemplate = {
                        title: (_p = template === null || template === void 0 ? void 0 : template.title) !== null && _p !== void 0 ? _p : defaultTemplate.title,
                        logoUrl: logoUrl,
                        currency: currency,
                        size: (_q = template === null || template === void 0 ? void 0 : template.size) !== null && _q !== void 0 ? _q : defaultTemplate.size,
                        includeTax: (_r = template === null || template === void 0 ? void 0 : template.includeTax) !== null && _r !== void 0 ? _r : defaultTemplate.includeTax,
                        includeVat: (_s = template === null || template === void 0 ? void 0 : template.includeVat) !== null && _s !== void 0 ? _s : defaultTemplate.includeVat,
                        includeDiscount: (_t = template === null || template === void 0 ? void 0 : template.includeDiscount) !== null && _t !== void 0 ? _t : defaultTemplate.includeDiscount,
                        includeDecimals: (_u = template === null || template === void 0 ? void 0 : template.includeDecimals) !== null && _u !== void 0 ? _u : defaultTemplate.includeDecimals,
                        includeUnits: (_v = template === null || template === void 0 ? void 0 : template.includeUnits) !== null && _v !== void 0 ? _v : defaultTemplate.includeUnits,
                        includeQr: (_w = template === null || template === void 0 ? void 0 : template.includeQr) !== null && _w !== void 0 ? _w : defaultTemplate.includeQr,
                        includePdf: (_x = template === null || template === void 0 ? void 0 : template.includePdf) !== null && _x !== void 0 ? _x : defaultTemplate.includePdf,
                        customerLabel: (_y = template === null || template === void 0 ? void 0 : template.customerLabel) !== null && _y !== void 0 ? _y : defaultTemplate.customerLabel,
                        fromLabel: (_z = template === null || template === void 0 ? void 0 : template.fromLabel) !== null && _z !== void 0 ? _z : defaultTemplate.fromLabel,
                        invoiceNoLabel: (_0 = template === null || template === void 0 ? void 0 : template.invoiceNoLabel) !== null && _0 !== void 0 ? _0 : defaultTemplate.invoiceNoLabel,
                        subtotalLabel: (_1 = template === null || template === void 0 ? void 0 : template.subtotalLabel) !== null && _1 !== void 0 ? _1 : defaultTemplate.subtotalLabel,
                        issueDateLabel: (_2 = template === null || template === void 0 ? void 0 : template.issueDateLabel) !== null && _2 !== void 0 ? _2 : defaultTemplate.issueDateLabel,
                        total_summary_label: (_3 = template === null || template === void 0 ? void 0 : template.totalSummaryLabel) !== null && _3 !== void 0 ? _3 : defaultTemplate.totalSummaryLabel,
                        dueDateLabel: (_4 = template === null || template === void 0 ? void 0 : template.dueDateLabel) !== null && _4 !== void 0 ? _4 : defaultTemplate.dueDateLabel,
                        discountLabel: (_5 = template === null || template === void 0 ? void 0 : template.discountLabel) !== null && _5 !== void 0 ? _5 : defaultTemplate.discountLabel,
                        descriptionLabel: (_6 = template === null || template === void 0 ? void 0 : template.descriptionLabel) !== null && _6 !== void 0 ? _6 : defaultTemplate.descriptionLabel,
                        priceLabel: (_7 = template === null || template === void 0 ? void 0 : template.priceLabel) !== null && _7 !== void 0 ? _7 : defaultTemplate.priceLabel,
                        quantityLabel: (_8 = template === null || template === void 0 ? void 0 : template.quantityLabel) !== null && _8 !== void 0 ? _8 : defaultTemplate.quantityLabel,
                        totalLabel: (_9 = template === null || template === void 0 ? void 0 : template.totalLabel) !== null && _9 !== void 0 ? _9 : defaultTemplate.totalLabel,
                        vatLabel: (_10 = template === null || template === void 0 ? void 0 : template.vatLabel) !== null && _10 !== void 0 ? _10 : defaultTemplate.vatLabel,
                        taxLabel: (_11 = template === null || template === void 0 ? void 0 : template.taxLabel) !== null && _11 !== void 0 ? _11 : defaultTemplate.taxLabel,
                        paymentLabel: (_12 = template === null || template === void 0 ? void 0 : template.paymentLabel) !== null && _12 !== void 0 ? _12 : defaultTemplate.paymentLabel,
                        noteLabel: (_13 = template === null || template === void 0 ? void 0 : template.noteLabel) !== null && _13 !== void 0 ? _13 : defaultTemplate.noteLabel,
                        dateFormat: dateFormat,
                        deliveryType: (_14 = template === null || template === void 0 ? void 0 : template.deliveryType) !== null && _14 !== void 0 ? _14 : defaultTemplate.deliveryType,
                        taxRate: (_15 = template === null || template === void 0 ? void 0 : template.taxRate) !== null && _15 !== void 0 ? _15 : defaultTemplate.taxRate,
                        vatRate: (_16 = template === null || template === void 0 ? void 0 : template.vatRate) !== null && _16 !== void 0 ? _16 : defaultTemplate.vatRate,
                        fromDetails: (_17 = template === null || template === void 0 ? void 0 : template.fromDetails) !== null && _17 !== void 0 ? _17 : defaultTemplate.fromDetails,
                        paymentDetails: (_18 = template === null || template === void 0 ? void 0 : template.paymentDetails) !== null && _18 !== void 0 ? _18 : defaultTemplate.paymentDetails,
                        timezone: timezone,
                        locale: locale,
                    };
                    return [2 /*return*/, {
                            // Default values first
                            id: (0, uuid_1.v4)(),
                            currency: currency,
                            status: "draft",
                            size: size,
                            includeTax: includeTax,
                            includeVat: !includeTax,
                            includeDiscount: false,
                            includeDecimals: false,
                            includePdf: false,
                            includeUnits: false,
                            includeQr: true,
                            invoiceNumber: nextInvoiceNumber,
                            timezone: timezone,
                            locale: locale,
                            fromDetails: savedTemplate.fromDetails,
                            paymentDetails: savedTemplate.paymentDetails,
                            customerDetails: undefined,
                            noteDetails: undefined,
                            customerId: undefined,
                            issueDate: new utc_1.UTCDate().toISOString(),
                            dueDate: (0, date_fns_1.addMonths)(new utc_1.UTCDate(), 1).toISOString(),
                            lineItems: [{ name: "", quantity: 0, price: 0, vat: 0 }],
                            tax: undefined,
                            token: undefined,
                            discount: undefined,
                            subtotal: undefined,
                            topBlock: undefined,
                            bottomBlock: undefined,
                            amount: undefined,
                            customerName: undefined,
                            logoUrl: undefined,
                            vat: undefined,
                            template: savedTemplate,
                        }];
            }
        });
    }); }),
    update: init_1.protectedProcedure
        .input(invoice_1.updateInvoiceSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, invoices_1.updateInvoice)(db, __assign(__assign({}, input), { teamId: teamId }))];
        });
    }); }),
    delete: init_1.protectedProcedure
        .input(invoice_1.deleteInvoiceSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, invoices_1.deleteInvoice)(db, {
                    id: input.id,
                    teamId: teamId,
                })];
        });
    }); }),
    draft: init_1.protectedProcedure
        .input(invoice_1.draftInvoiceSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId, session = _c.session;
        return __generator(this, function (_d) {
            return [2 /*return*/, (0, invoices_1.draftInvoice)(db, __assign(__assign({}, input), { teamId: teamId, userId: session === null || session === void 0 ? void 0 : session.user.id, paymentDetails: (0, parse_1.parseInputValue)(input.paymentDetails), fromDetails: (0, parse_1.parseInputValue)(input.fromDetails), customerDetails: (0, parse_1.parseInputValue)(input.customerDetails), noteDetails: (0, parse_1.parseInputValue)(input.noteDetails) }))];
        });
    }); }),
    create: init_1.protectedProcedure
        .input(invoice_1.createInvoiceSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var data;
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0: return [4 /*yield*/, (0, invoices_1.updateInvoice)(db, {
                        id: input.id,
                        status: "unpaid",
                        teamId: teamId,
                    })];
                case 1:
                    data = _d.sent();
                    if (!data) {
                        throw new Error("Invoice not found");
                    }
                    return [4 /*yield*/, v3_1.tasks.trigger("generate-invoice", {
                            invoiceId: data.id,
                            deliveryType: input.deliveryType,
                        })];
                case 2:
                    _d.sent();
                    return [2 /*return*/, data];
            }
        });
    }); }),
    remind: init_1.protectedProcedure
        .input(invoice_1.remindInvoiceSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var input = _b.input, _c = _b.ctx, db = _c.db, teamId = _c.teamId;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0: return [4 /*yield*/, v3_1.tasks.trigger("send-invoice-reminder", {
                        invoiceId: input.id,
                    })];
                case 1:
                    _d.sent();
                    return [2 /*return*/, (0, invoices_1.updateInvoice)(db, {
                            id: input.id,
                            teamId: teamId,
                            reminderSentAt: input.date,
                        })];
            }
        });
    }); }),
    duplicate: init_1.protectedProcedure
        .input(invoice_1.duplicateInvoiceSchema)
        .mutation(function (_a) { return __awaiter(void 0, [_a], void 0, function (_b) {
        var nextInvoiceNumber;
        var input = _b.input, _c = _b.ctx, db = _c.db, session = _c.session, teamId = _c.teamId;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0: return [4 /*yield*/, (0, invoices_1.getNextInvoiceNumber)(db, teamId)];
                case 1:
                    nextInvoiceNumber = _d.sent();
                    return [2 /*return*/, (0, invoices_1.duplicateInvoice)(db, {
                            id: input.id,
                            userId: session === null || session === void 0 ? void 0 : session.user.id,
                            invoiceNumber: nextInvoiceNumber,
                            teamId: teamId,
                        })];
            }
        });
    }); }),
});
