"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.withPrimaryReadAfterWrite = void 0;
var logger_1 = require("@api/utils/logger");
var lru_cache_1 = require("lru-cache");
// In-memory map to track teams who recently performed mutations.
// Note: This map is per server instance, and we typically run 1 instance per region.
// Otherwise, we would need to share this state with Redis or a similar external store.
// Key: teamId, Value: timestamp when they should be able to use replicas again
var cache = new lru_cache_1.LRUCache({
    max: 5000, // up to 5k entries
    ttl: 10000, // 10 seconds in milliseconds
});
// The window time in milliseconds to handle replication lag (10 seconds)
var REPLICATION_LAG_WINDOW = 10000;
// Database middleware that handles replication lag based on mutation operations
// For mutations: always use primary DB
// For queries: use primary DB if the team recently performed a mutation
var withPrimaryReadAfterWrite = function (opts) { return __awaiter(void 0, void 0, void 0, function () {
    var ctx, type, next, teamId, expiryTime, dbWithPrimary, timestamp, now, remainingMs, dbWithPrimary, dbWithPrimary, start, result, duration;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                ctx = opts.ctx, type = opts.type, next = opts.next;
                teamId = ctx.teamId;
                if (teamId) {
                    // For mutations, always use primary DB and update the team's timestamp
                    if (type === "mutation") {
                        expiryTime = Date.now() + REPLICATION_LAG_WINDOW;
                        cache.set(teamId, expiryTime);
                        logger_1.logger.info({
                            msg: "Using primary DB for mutation",
                            teamId: teamId,
                            operationType: type,
                            replicaBlockUntil: new Date(expiryTime).toISOString(),
                        });
                        dbWithPrimary = ctx.db;
                        if (dbWithPrimary.usePrimaryOnly) {
                            ctx.db = dbWithPrimary.usePrimaryOnly();
                        }
                        // If usePrimaryOnly doesn't exist, we're already using the primary DB
                    }
                    // For queries, check if the team recently performed a mutation
                    else {
                        timestamp = cache.get(teamId);
                        now = Date.now();
                        // If the timestamp exists and hasn't expired, use primary DB
                        if (timestamp && now < timestamp) {
                            remainingMs = timestamp - now;
                            logger_1.logger.info({
                                msg: "Using primary DB for query after recent mutation",
                                teamId: teamId,
                                operationType: type,
                                replicaBlockRemainingMs: remainingMs,
                                replicaBlockUntil: new Date(timestamp).toISOString(),
                            });
                            dbWithPrimary = ctx.db;
                            if (dbWithPrimary.usePrimaryOnly) {
                                ctx.db = dbWithPrimary.usePrimaryOnly();
                            }
                            // If usePrimaryOnly doesn't exist, we're already using the primary DB
                        }
                        else {
                            logger_1.logger.debug({
                                msg: "Using replica DB for query",
                                teamId: teamId,
                                operationType: type,
                                recentMutation: !!timestamp,
                            });
                        }
                    }
                }
                else {
                    logger_1.logger.debug({
                        msg: "No team ID in context, using primary DB",
                        operationType: type,
                    });
                    dbWithPrimary = ctx.db;
                    if (dbWithPrimary.usePrimaryOnly) {
                        ctx.db = dbWithPrimary.usePrimaryOnly();
                    }
                    // If usePrimaryOnly doesn't exist, we're already using the primary DB
                }
                start = performance.now();
                return [4 /*yield*/, next({ ctx: ctx })];
            case 1:
                result = _a.sent();
                duration = performance.now() - start;
                if (duration > 500) {
                    logger_1.logger.warn({
                        msg: "Slow DB operation detected",
                        teamId: teamId,
                        operationType: type,
                        durationMs: Math.round(duration),
                    });
                }
                return [2 /*return*/, result];
        }
    });
}); };
exports.withPrimaryReadAfterWrite = withPrimaryReadAfterWrite;
