"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.withTeamPermission = void 0;
var server_1 = require("@trpc/server");
var lru_cache_1 = require("lru-cache");
// In-memory cache to check if a user has access to a team
// Note: This cache is per server instance, and we typically run 1 instance per region.
// Otherwise, we would need to share this state with Redis or a similar external store.
var cache = new lru_cache_1.LRUCache({
    max: 5000, // up to 5k entries (adjust based on memory)
    ttl: 1000 * 60 * 30, // 30 minutes in milliseconds
});
var withTeamPermission = function (opts) { return __awaiter(void 0, void 0, void 0, function () {
    var ctx, next, userId, result, teamId, cacheKey, hasAccess;
    var _a, _b;
    return __generator(this, function (_c) {
        switch (_c.label) {
            case 0:
                ctx = opts.ctx, next = opts.next;
                userId = (_b = (_a = ctx.session) === null || _a === void 0 ? void 0 : _a.user) === null || _b === void 0 ? void 0 : _b.id;
                if (!userId) {
                    throw new server_1.TRPCError({
                        code: "UNAUTHORIZED",
                        message: "No permission to access this team",
                    });
                }
                return [4 /*yield*/, ctx.db.query.users.findFirst({
                        with: {
                            usersOnTeams: {
                                columns: {
                                    id: true,
                                    teamId: true,
                                },
                            },
                        },
                        where: function (users, _a) {
                            var eq = _a.eq;
                            return eq(users.id, userId);
                        },
                    })];
            case 1:
                result = _c.sent();
                if (!result) {
                    throw new server_1.TRPCError({
                        code: "NOT_FOUND",
                        message: "User not found",
                    });
                }
                teamId = result.teamId;
                // If teamId is null, user has no team assigned but this is now allowed
                if (teamId !== null) {
                    cacheKey = "user:".concat(userId, ":team:").concat(teamId);
                    hasAccess = cache.get(cacheKey);
                    if (hasAccess === undefined) {
                        hasAccess = result.usersOnTeams.some(function (membership) { return membership.teamId === teamId; });
                        cache.set(cacheKey, hasAccess);
                    }
                    if (!hasAccess) {
                        throw new server_1.TRPCError({
                            code: "FORBIDDEN",
                            message: "No permission to access this team",
                        });
                    }
                }
                return [2 /*return*/, next({
                        ctx: {
                            session: ctx.session,
                            teamId: teamId,
                            db: ctx.db,
                        },
                    })];
        }
    });
}); };
exports.withTeamPermission = withTeamPermission;
