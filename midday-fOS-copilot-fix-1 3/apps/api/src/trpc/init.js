"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.protectedProcedure = exports.publicProcedure = exports.createCallerFactory = exports.createTRPCRouter = exports.createTRPCContext = void 0;
var db_1 = require("@api/db");
var supabase_1 = require("@api/services/supabase");
var auth_1 = require("@api/utils/auth");
var geo_1 = require("@api/utils/geo");
var server_1 = require("@trpc/server");
var superjson_1 = require("superjson");
var primary_read_after_write_1 = require("./middleware/primary-read-after-write");
var team_permission_1 = require("./middleware/team-permission");
var createTRPCContext = function (_, c) { return __awaiter(void 0, void 0, void 0, function () {
    var accessToken, session, supabase, db, geo;
    var _a;
    return __generator(this, function (_b) {
        switch (_b.label) {
            case 0:
                accessToken = (_a = c.req.header("Authorization")) === null || _a === void 0 ? void 0 : _a.split(" ")[1];
                return [4 /*yield*/, (0, auth_1.verifyAccessToken)(accessToken)];
            case 1:
                session = _b.sent();
                return [4 /*yield*/, (0, supabase_1.createClient)(accessToken)];
            case 2:
                supabase = _b.sent();
                return [4 /*yield*/, (0, db_1.connectDb)()];
            case 3:
                db = _b.sent();
                geo = (0, geo_1.getGeoContext)(c.req);
                return [2 /*return*/, {
                        session: session,
                        supabase: supabase,
                        db: db,
                        geo: geo,
                    }];
        }
    });
}); };
exports.createTRPCContext = createTRPCContext;
var t = server_1.initTRPC.context().create({
    transformer: superjson_1.default,
});
exports.createTRPCRouter = t.router;
exports.createCallerFactory = t.createCallerFactory;
var withPrimaryDbMiddleware = t.middleware(function (opts) { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        return [2 /*return*/, (0, primary_read_after_write_1.withPrimaryReadAfterWrite)({
                ctx: opts.ctx,
                type: opts.type,
                next: opts.next,
            })];
    });
}); });
var withTeamPermissionMiddleware = t.middleware(function (opts) { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        return [2 /*return*/, (0, team_permission_1.withTeamPermission)({
                ctx: opts.ctx,
                next: opts.next,
            })];
    });
}); });
exports.publicProcedure = t.procedure.use(withPrimaryDbMiddleware);
exports.protectedProcedure = t.procedure
    .use(withTeamPermissionMiddleware) // NOTE: This is needed to ensure that the teamId is set in the context
    .use(withPrimaryDbMiddleware)
    .use(function (opts) { return __awaiter(void 0, void 0, void 0, function () {
    var _a, teamId, session;
    return __generator(this, function (_b) {
        _a = opts.ctx, teamId = _a.teamId, session = _a.session;
        if (!session) {
            throw new server_1.TRPCError({ code: "UNAUTHORIZED" });
        }
        return [2 /*return*/, opts.next({
                ctx: {
                    teamId: teamId,
                    session: session,
                },
            })];
    });
}); });
