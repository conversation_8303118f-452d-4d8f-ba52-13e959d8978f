import { ChartSelectors } from "@/components/charts/chart-selectors";

export function ServicesCharts({ disabled = false }: { disabled?: boolean }) {
  return (
    <div>
      <ChartSelectors />
      
      <div className="mt-8 grid gap-6 grid-cols-1 lg:grid-cols-2 xl:grid-cols-4">
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Services Planned</h3>
          <p className="text-2xl font-bold">16</p>
          <p className="text-xs text-muted-foreground">This month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Active Teams</h3>
          <p className="text-2xl font-bold">8</p>
          <p className="text-xs text-muted-foreground">Worship teams</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Volunteers</h3>
          <p className="text-2xl font-bold">64</p>
          <p className="text-xs text-muted-foreground">+3 from last month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Songs Planned</h3>
          <p className="text-2xl font-bold">127</p>
          <p className="text-xs text-muted-foreground">This month</p>
        </div>
      </div>
    </div>
  );
}