import { ChartSelectors } from "@/components/charts/chart-selectors";

export function EventsCharts({ disabled = false }: { disabled?: boolean }) {
  return (
    <div>
      <ChartSelectors />
      
      <div className="mt-8 grid gap-6 grid-cols-1 lg:grid-cols-2 xl:grid-cols-4">
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Total Events</h3>
          <p className="text-2xl font-bold">24</p>
          <p className="text-xs text-muted-foreground">+12% from last month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Total Attendance</h3>
          <p className="text-2xl font-bold">1,247</p>
          <p className="text-xs text-muted-foreground">+8% from last month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Average per Event</h3>
          <p className="text-2xl font-bold">52</p>
          <p className="text-xs text-muted-foreground">-3% from last month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">New Visitors</h3>
          <p className="text-2xl font-bold">156</p>
          <p className="text-xs text-muted-foreground">+22% from last month</p>
        </div>
      </div>
    </div>
  );
}