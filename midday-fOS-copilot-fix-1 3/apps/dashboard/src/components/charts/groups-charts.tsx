import { ChartSelectors } from "@/components/charts/chart-selectors";

export function GroupsCharts({ disabled = false }: { disabled?: boolean }) {
  return (
    <div>
      <ChartSelectors />
      
      <div className="mt-8 grid gap-6 grid-cols-1 lg:grid-cols-2 xl:grid-cols-4">
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Total Groups</h3>
          <p className="text-2xl font-bold">18</p>
          <p className="text-xs text-muted-foreground">+2 new this month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Active Members</h3>
          <p className="text-2xl font-bold">342</p>
          <p className="text-xs text-muted-foreground">+15 from last month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Average Group Size</h3>
          <p className="text-2xl font-bold">19</p>
          <p className="text-xs text-muted-foreground">Optimal size range</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Ministry Teams</h3>
          <p className="text-2xl font-bold">12</p>
          <p className="text-xs text-muted-foreground">All actively serving</p>
        </div>
      </div>
    </div>
  );
}