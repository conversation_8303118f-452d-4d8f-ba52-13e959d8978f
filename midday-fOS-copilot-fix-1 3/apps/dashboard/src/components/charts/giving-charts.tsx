import { ChartSelectors } from "@/components/charts/chart-selectors";

export function GivingCharts({ disabled = false }: { disabled?: boolean }) {
  return (
    <div>
      <ChartSelectors />
      
      <div className="mt-8 grid gap-6 grid-cols-1 lg:grid-cols-2 xl:grid-cols-4">
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Total Giving</h3>
          <p className="text-2xl font-bold">$52,340</p>
          <p className="text-xs text-muted-foreground">+8% from last month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Active Givers</h3>
          <p className="text-2xl font-bold">324</p>
          <p className="text-xs text-muted-foreground">+12 from last month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Average Gift</h3>
          <p className="text-2xl font-bold">$162</p>
          <p className="text-xs text-muted-foreground">-3% from last month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Online Giving</h3>
          <p className="text-2xl font-bold">78%</p>
          <p className="text-xs text-muted-foreground">+5% from last month</p>
        </div>
      </div>
    </div>
  );
}