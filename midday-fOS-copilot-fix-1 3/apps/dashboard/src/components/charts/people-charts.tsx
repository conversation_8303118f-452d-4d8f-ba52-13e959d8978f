import { ChartSelectors } from "@/components/charts/chart-selectors";

export function PeopleCharts({ disabled = false }: { disabled?: boolean }) {
  return (
    <div>
      <ChartSelectors />
      
      <div className="mt-8 grid gap-6 grid-cols-1 lg:grid-cols-2 xl:grid-cols-4">
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Total Members</h3>
          <p className="text-2xl font-bold">1,247</p>
          <p className="text-xs text-muted-foreground">+15 new this month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Active Groups</h3>
          <p className="text-2xl font-bold">32</p>
          <p className="text-xs text-muted-foreground">+2 from last month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Volunteers</h3>
          <p className="text-2xl font-bold">186</p>
          <p className="text-xs text-muted-foreground">+12 from last month</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">New Visitors</h3>
          <p className="text-2xl font-bold">43</p>
          <p className="text-xs text-muted-foreground">This month</p>
        </div>
      </div>
    </div>
  );
}