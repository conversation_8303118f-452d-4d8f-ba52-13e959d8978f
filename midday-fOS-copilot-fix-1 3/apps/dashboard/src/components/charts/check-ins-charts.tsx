import { ChartSelectors } from "@/components/charts/chart-selectors";

export function CheckInsCharts({ disabled = false }: { disabled?: boolean }) {
  return (
    <div>
      <ChartSelectors />
      
      <div className="mt-8 grid gap-6 grid-cols-1 lg:grid-cols-2 xl:grid-cols-4">
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Today's Check-ins</h3>
          <p className="text-2xl font-bold">127</p>
          <p className="text-xs text-muted-foreground">Sunday service</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">This Week</h3>
          <p className="text-2xl font-bold">284</p>
          <p className="text-xs text-muted-foreground">+18% from last week</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">First-time Visitors</h3>
          <p className="text-2xl font-bold">8</p>
          <p className="text-xs text-muted-foreground">New families welcomed</p>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Attendance Rate</h3>
          <p className="text-2xl font-bold">82%</p>
          <p className="text-xs text-muted-foreground">Of registered members</p>
        </div>
      </div>
    </div>
  );
}